<?php
// /ajax/delete_category.php
session_start();
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../includes/auth.php';

// Ensure only admins can delete categories
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
  header('Content-Type: application/json');
  echo json_encode(['success' => false, 'message' => 'Unauthorized access.']);
  exit;
}

// Set content type to JSON
header('Content-Type: application/json');

// Debug information
$debug = [];
$debug['post_data'] = $_POST;
$debug['raw_input'] = file_get_contents('php://input');
$debug['session'] = $_SESSION;

try {
  // Get category ID from either POST or JSON input
  $json_input = json_decode(file_get_contents('php://input'), true);
  $id = intval($_POST['id'] ?? $json_input['id'] ?? 0);
  $debug['category_id'] = $id;
  $debug['json_input'] = $json_input;

  // Validate input
  if ($id <= 0) {
    throw new Exception('Valid category ID is required.');
  }

  // Check if category exists
  $check = $pdo->prepare("SELECT id FROM categories WHERE id = ?");
  $check->execute([$id]);
  if (!$check->fetch()) {
    throw new Exception('The category does not exist.');
  }

  // Begin transaction to ensure all operations succeed or fail together
  $pdo->beginTransaction();

  try {
    // Delete any costs associated with this category
    $stmt = $pdo->prepare("DELETE FROM costs WHERE category_id = ?");
    $stmt->execute([$id]);

    // Delete the category (subcategories will be deleted automatically due to ON DELETE CASCADE)
    $stmt = $pdo->prepare("DELETE FROM categories WHERE id = ?");
    $result = $stmt->execute([$id]);

    if (!$result) {
      throw new Exception('Failed to delete category from database.');
    }

    // Commit the transaction
    $pdo->commit();
  } catch (Exception $e) {
    // Rollback the transaction on error
    $pdo->rollBack();
    throw $e;
  }

  // Return success response
  echo json_encode([
    'success' => true,
    'message' => 'Category deleted successfully.',
    'debug' => $debug
  ]);

} catch (Exception $e) {
  // Return error response
  echo json_encode([
    'success' => false,
    'message' => $e->getMessage(),
    'debug' => $debug
  ]);
}

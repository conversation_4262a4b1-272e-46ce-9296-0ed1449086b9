<?php
// File: /admin/category_links.php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// 1) Load config & auth
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../includes/auth.php';
authenticate_admin();

// 2) Fetch categories for dropdowns
try {
    $stmt = $pdo->query("SELECT id, name, include_in_cost FROM categories ORDER BY name");
    $categories = $stmt->fetchAll();
    
    // Separate categories that can be multipliers vs targets
    $multiplier_categories = array_filter($categories, function($cat) {
        return $cat['include_in_cost'] == 1;
    });
    
    $target_categories = array_filter($categories, function($cat) {
        return $cat['include_in_cost'] == 0;
    });
} catch (PDOException $e) {
    $categories = [];
    $multiplier_categories = [];
    $target_categories = [];
    $loadError = $e->getMessage();
}

// 3) Render page
?>
<?php include __DIR__ . '/../includes/header.php'; ?>

<main class="container">
  <h1 class="section-title">
    <i class="fas fa-link"></i> Category Links Management
  </h1>
  
  <p class="section-description">
    Create multiplier relationships between categories. When a multiplier category is used, 
    it will include the total cost of the linked target category multiplied by the specified factor.
  </p>

  <!-- Add New Category Link -->
  <section class="card mb-4">
    <div class="card-header">
      <h2><i class="fas fa-plus-circle"></i> Add New Category Link</h2>
    </div>
    <form id="category-link-form" action="javascript:;" class="card-body">
      <div class="form-row">
        <div class="form-group">
          <label for="multiplier-category">Multiplier Category</label>
          <select id="multiplier-category" name="multiplier_category_id" required>
            <option value="">Select multiplier category...</option>
            <?php foreach ($multiplier_categories as $cat): ?>
              <option value="<?= $cat['id'] ?>"><?= htmlspecialchars($cat['name']) ?></option>
            <?php endforeach; ?>
          </select>
          <small class="form-help">Category that will trigger the multiplication</small>
        </div>
        
        <div class="form-group">
          <label for="target-category">Target Category</label>
          <select id="target-category" name="target_category_id" required>
            <option value="">Select target category...</option>
            <?php foreach ($target_categories as $cat): ?>
              <option value="<?= $cat['id'] ?>"><?= htmlspecialchars($cat['name']) ?></option>
            <?php endforeach; ?>
          </select>
          <small class="form-help">Category excluded from cost calculations (will be multiplied)</small>
        </div>
        
        <div class="form-group">
          <label for="multiplier">Multiplier</label>
          <input type="number" 
                 id="multiplier" 
                 name="multiplier" 
                 step="0.01" 
                 min="0.01" 
                 value="1.0" 
                 required />
          <small class="form-help">Factor to multiply the target category's total cost</small>
        </div>
      </div>
      
      <button type="submit" class="btn btn-primary">
        <i class="fas fa-link"></i> Create Link
      </button>
    </form>
  </section>

  <!-- Existing Category Links -->
  <section class="card">
    <div class="card-header">
      <h2><i class="fas fa-list"></i> Existing Category Links</h2>
    </div>
    <div class="card-body">
      <div id="links-container">
        <p class="text-center">Loading category links...</p>
      </div>
    </div>
  </section>
</main>

<?php include __DIR__ . '/../includes/footer.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const linkForm = document.getElementById('category-link-form');
  const linksContainer = document.getElementById('links-container');

  // Load existing links
  loadCategoryLinks();

  // Handle form submission
  linkForm.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    try {
      const formData = new FormData(linkForm);
      const response = await fetch('../ajax/save_category_link.php', {
        method: 'POST',
        body: formData
      });
      
      const data = await response.json();
      
      if (data.success) {
        alert(data.message);
        linkForm.reset();
        loadCategoryLinks(); // Reload the links
      } else {
        alert('Error: ' + data.message);
      }
    } catch (error) {
      alert('Error: ' + error.message);
    }
  });

  async function loadCategoryLinks() {
    try {
      const response = await fetch('../ajax/get_category_links.php');
      const data = await response.json();
      
      if (data.success) {
        displayCategoryLinks(data.links);
      } else {
        linksContainer.innerHTML = '<p class="text-danger">Error loading links: ' + data.message + '</p>';
      }
    } catch (error) {
      linksContainer.innerHTML = '<p class="text-danger">Error loading links: ' + error.message + '</p>';
    }
  }

  function displayCategoryLinks(links) {
    if (links.length === 0) {
      linksContainer.innerHTML = '<p class="text-muted">No category links found. Create one above to get started.</p>';
      return;
    }

    let html = '<div class="table-container"><table class="data-table"><thead><tr>';
    html += '<th>Multiplier Category</th>';
    html += '<th>Target Category</th>';
    html += '<th>Multiplier</th>';
    html += '<th>Created</th>';
    html += '<th>Actions</th>';
    html += '</tr></thead><tbody>';

    links.forEach(link => {
      html += '<tr>';
      html += '<td><strong>' + escapeHtml(link.multiplier_category_name) + '</strong></td>';
      html += '<td>' + escapeHtml(link.target_category_name) + '</td>';
      html += '<td>' + parseFloat(link.multiplier).toFixed(2) + 'x</td>';
      html += '<td>' + new Date(link.created_at).toLocaleDateString() + '</td>';
      html += '<td>';
      html += '<button class="btn btn-sm btn-danger" onclick="deleteCategoryLink(' + link.id + ')">';
      html += '<i class="fas fa-trash"></i> Delete';
      html += '</button>';
      html += '</td>';
      html += '</tr>';
    });

    html += '</tbody></table></div>';
    linksContainer.innerHTML = html;
  }

  // Make deleteCategoryLink available globally
  window.deleteCategoryLink = async function(linkId) {
    if (!confirm('Are you sure you want to delete this category link?')) {
      return;
    }

    try {
      const formData = new FormData();
      formData.append('link_id', linkId);
      
      const response = await fetch('../ajax/delete_category_link.php', {
        method: 'POST',
        body: formData
      });
      
      const data = await response.json();
      
      if (data.success) {
        alert(data.message);
        loadCategoryLinks(); // Reload the links
      } else {
        alert('Error: ' + data.message);
      }
    } catch (error) {
      alert('Error: ' + error.message);
    }
  };

  function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
});
</script>

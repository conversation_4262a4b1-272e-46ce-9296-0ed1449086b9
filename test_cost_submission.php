<?php
// /test_cost_submission.php
// Test page to verify cost submission works without duplicates
session_start();
require_once __DIR__ . '/config/db.php';

// Simple auth check
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = ['role' => 'admin']; // Temporary for testing
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cost Submission Test</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Cost Submission Test</h1>
        
        <div class="test-section">
            <h2>Test Cost Form</h2>
            <p>This form tests the cost submission with multiple duplicate prevention mechanisms:</p>
            <ul>
                <li>✅ JavaScript submission flag</li>
                <li>✅ Button disable during submission</li>
                <li>✅ Unique submission tokens</li>
                <li>✅ Server-side duplicate detection</li>
            </ul>
            
            <form id="test-cost-form">
                <div class="form-group">
                    <label for="description">Description</label>
                    <input type="text" id="description" name="description" value="Test Cost Entry" required>
                </div>
                <div class="form-group">
                    <label for="amount">Amount</label>
                    <input type="number" id="amount" name="amount" value="100.00" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="category_id">Category ID</label>
                    <input type="number" id="category_id" name="category_id" value="1" required>
                    <small>Use an existing category ID from your database</small>
                </div>
                <div class="form-group">
                    <label for="subcategory_id">Subcategory ID</label>
                    <input type="number" id="subcategory_id" name="subcategory_id" value="1" required>
                    <small>Use an existing subcategory ID from your database</small>
                </div>
                <div class="form-group">
                    <label for="rate_type">Rate Type</label>
                    <select id="rate_type" name="rate_type">
                        <option value="daily">Daily</option>
                        <option value="monthly">Monthly</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="num_days">Number of Days</label>
                    <input type="number" id="num_days" name="num_days" value="1" min="1" required>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Submit Test Cost
                </button>
            </form>
        </div>

        <div class="test-section">
            <h2>Submission Log</h2>
            <p>This log shows each submission attempt to detect duplicates:</p>
            <div id="submission-log" class="log-output">
                <em>No submissions yet...</em>
            </div>
            <button id="clear-log" class="btn btn-secondary">
                <i class="fas fa-trash"></i> Clear Log
            </button>
        </div>

        <div class="test-section">
            <h2>Database Check</h2>
            <p>Check recent cost entries in the database:</p>
            <button id="check-db" class="btn btn-success">
                <i class="fas fa-database"></i> Check Recent Costs
            </button>
            <div id="db-results" class="log-output" style="display: none;">
                <!-- Results will be shown here -->
            </div>
        </div>

        <div class="test-section">
            <h2>Navigation</h2>
            <a href="admin/costs.php" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> Back to Costs Page
            </a>
            <a href="admin/categories.php" class="btn btn-secondary">
                <i class="fas fa-tags"></i> Manage Categories
            </a>
        </div>
    </div>

    <script>
    let submissionCount = 0;
    let isSubmitting = false;

    function addToLog(message) {
        const log = document.getElementById('submission-log');
        const timestamp = new Date().toLocaleTimeString();
        const entry = `[${timestamp}] ${message}`;

        if (log.textContent.includes('No submissions yet')) {
            log.innerHTML = '';
        }

        const div = document.createElement('div');
        div.textContent = entry;
        log.appendChild(div);
        log.scrollTop = log.scrollHeight;
    }

    document.addEventListener('DOMContentLoaded', function() {
        const testForm = document.getElementById('test-cost-form');

        testForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            // Prevent multiple simultaneous submissions
            if (isSubmitting) {
                addToLog(`⚠️ BLOCKED: Submission already in progress`);
                return;
            }
            isSubmitting = true;

            submissionCount++;
            addToLog(`Submission #${submissionCount} started`);

            const submitButton = testForm.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';

            try {
                const formData = new FormData(testForm);

                // Add unique submission token
                const submissionToken = Date.now() + '-' + Math.random().toString(36).substr(2, 9);
                formData.append('submission_token', submissionToken);
                addToLog(`Token: ${submissionToken}`);
                addToLog(`Sending data: ${formData.get('description')}`);

                const response = await fetch('ajax/save_cost.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    addToLog(`✅ SUCCESS: ${data.message} (ID: ${data.id})`);
                } else {
                    addToLog(`❌ ERROR: ${data.message}`);
                }
            } catch (error) {
                addToLog(`💥 EXCEPTION: ${error.message}`);
            } finally {
                submitButton.disabled = false;
                submitButton.innerHTML = originalText;
                isSubmitting = false;
                addToLog(`Submission #${submissionCount} completed`);
            }
        });
        
        // Clear log button
        document.getElementById('clear-log').addEventListener('click', function() {
            document.getElementById('submission-log').innerHTML = '<em>No submissions yet...</em>';
            submissionCount = 0;
        });
        
        // Check database button
        document.getElementById('check-db').addEventListener('click', async function() {
            const resultsDiv = document.getElementById('db-results');
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = 'Loading...';
            
            try {
                // Simple query to check recent costs
                const response = await fetch('ajax/get_recent_costs.php');
                const data = await response.json();
                
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <h4>Recent Costs (Last 10):</h4>
                        ${data.costs.map(cost => 
                            `<div>${cost.id}: ${cost.description} - R${cost.amount} (${cost.created_at})</div>`
                        ).join('')}
                    `;
                } else {
                    resultsDiv.innerHTML = `Error: ${data.message}`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `Error: ${error.message}`;
            }
        });
    });
    </script>
</body>
</html>

<?php
/**
 * Simple test to check if costs are being saved and can be retrieved
 */
require_once __DIR__ . '/config/db.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Simple Costs Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; padding: 10px; background: #e8f5e8; border-radius: 5px; margin: 10px 0; }
        .error { color: red; padding: 10px; background: #ffe8e8; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; padding: 10px; background: #e8f0ff; border-radius: 5px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>";

echo "<h1>🧪 Simple Costs Test</h1>";

try {
    // Test 1: Basic database connection
    echo "<div class='success'>✅ Database connected</div>";
    
    // Test 2: Count costs
    $countResult = $pdo->query("SELECT COUNT(*) as total FROM costs");
    $totalCosts = $countResult->fetch()['total'];
    echo "<div class='info'>📊 Total costs in database: <strong>{$totalCosts}</strong></div>";
    
    // Test 3: Show all costs (simple query)
    if ($totalCosts > 0) {
        echo "<h2>📝 All Costs (Raw Data)</h2>";
        
        $costs = $pdo->query("SELECT * FROM costs ORDER BY created_at DESC")->fetchAll();
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Description</th><th>Amount</th><th>Category ID</th><th>Subcategory ID</th><th>Created</th></tr>";
        
        foreach ($costs as $cost) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($cost['id']) . "</td>";
            echo "<td>" . htmlspecialchars($cost['description']) . "</td>";
            echo "<td>R" . number_format($cost['amount'], 2) . "</td>";
            echo "<td>" . htmlspecialchars($cost['category_id'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($cost['subcategory_id'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($cost['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Test 4: Check categories
        echo "<h2>🏷️ Categories Check</h2>";
        $categoriesCount = $pdo->query("SELECT COUNT(*) as total FROM categories")->fetch()['total'];
        echo "<div class='info'>Categories: <strong>{$categoriesCount}</strong></div>";
        
        if ($categoriesCount > 0) {
            $categories = $pdo->query("SELECT * FROM categories")->fetchAll();
            echo "<table>";
            echo "<tr><th>ID</th><th>Name</th></tr>";
            foreach ($categories as $cat) {
                echo "<tr><td>{$cat['id']}</td><td>" . htmlspecialchars($cat['name']) . "</td></tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='error'>❌ No categories found! This is likely why costs aren't displaying.</div>";
            echo "<div class='info'>💡 Add some categories first, then your costs should display.</div>";
        }
        
        // Test 5: Check subcategories
        echo "<h2>📂 Subcategories Check</h2>";
        $subcategoriesCount = $pdo->query("SELECT COUNT(*) as total FROM subcategories")->fetch()['total'];
        echo "<div class='info'>Subcategories: <strong>{$subcategoriesCount}</strong></div>";
        
        if ($subcategoriesCount > 0) {
            $subcategories = $pdo->query("SELECT s.*, c.name as category_name FROM subcategories s LEFT JOIN categories c ON s.category_id = c.id")->fetchAll();
            echo "<table>";
            echo "<tr><th>ID</th><th>Name</th><th>Category</th></tr>";
            foreach ($subcategories as $sub) {
                echo "<tr><td>{$sub['id']}</td><td>" . htmlspecialchars($sub['name']) . "</td><td>" . htmlspecialchars($sub['category_name'] ?? 'N/A') . "</td></tr>";
            }
            echo "</table>";
        }
        
        // Test 6: Test the JOIN query
        echo "<h2>🔗 JOIN Query Test</h2>";
        try {
            $joinQuery = "
                SELECT co.*, ca.name AS category, su.name AS subcategory
                FROM costs co 
                LEFT JOIN categories ca ON co.category_id = ca.id 
                LEFT JOIN subcategories su ON co.subcategory_id = su.id 
                ORDER BY co.created_at DESC
                LIMIT 5
            ";
            
            $joinResults = $pdo->query($joinQuery)->fetchAll();
            
            if (!empty($joinResults)) {
                echo "<div class='success'>✅ JOIN query works</div>";
                echo "<table>";
                echo "<tr><th>ID</th><th>Description</th><th>Amount</th><th>Category</th><th>Subcategory</th></tr>";
                foreach ($joinResults as $result) {
                    echo "<tr>";
                    echo "<td>{$result['id']}</td>";
                    echo "<td>" . htmlspecialchars($result['description']) . "</td>";
                    echo "<td>R" . number_format($result['amount'], 2) . "</td>";
                    echo "<td>" . htmlspecialchars($result['category'] ?? 'NULL') . "</td>";
                    echo "<td>" . htmlspecialchars($result['subcategory'] ?? 'NULL') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<div class='error'>❌ JOIN query returned no results</div>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ JOIN query failed: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
        
    } else {
        echo "<div class='error'>❌ No costs found in database</div>";
        echo "<div class='info'>💡 Try adding a cost using the form on the costs page</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<h2>🔧 Next Steps</h2>";
echo "<ul>";
echo "<li>If costs exist but categories are missing → Add categories first</li>";
echo "<li>If no costs exist → Try adding a cost using the form</li>";
echo "<li>If JOIN query fails → Check your database structure</li>";
echo "<li>Check browser console (F12) for JavaScript errors</li>";
echo "</ul>";

echo "</body></html>";
?>

# Environment Setup Guide

## 🔄 Easy Switching Between Localhost and InfinityFree

This system now supports easy switching between development (localhost) and production (InfinityFree) environments.

## 🚀 Quick Start for Localhost Development

### Option 1: Automatic Setup
1. **Start XAMPP/WAMP** - Make sure Apache and MySQL are running
2. **Run setup**: Go to `http://localhost/your-project-folder/setup_localhost.php`
3. **Done!** The system will automatically detect localhost and set up everything

### Option 2: Manual Setup
1. **Edit environment**: Go to `http://localhost/your-project-folder/switch_environment.php`
2. **Select "Development"** and click "Switch Environment"
3. **Run setup**: Go to `setup_localhost.php`

## 📊 Accessing phpMyAdmin

Once running on localhost, you can access phpMyAdmin at:
- **URL**: `http://localhost/phpmyadmin`
- **Username**: `root`
- **Password**: (usually empty)

## ⚙️ Environment Configuration

### Automatic Detection (Recommended)
The system automatically detects your environment:
- **Localhost**: When running on `localhost`, `127.0.0.1`, or `.local` domains
- **Production**: When running on any other domain (like InfinityFree)

### Manual Override
Edit `config/environment.php` and change:
```php
$ENVIRONMENT = 'auto'; // Change to 'development' or 'production'
```

## 🗄️ Database Configurations

### Development (Localhost)
```
Host: localhost
Database: cost_calculator
Username: root
Password: (empty)
```

### Production (InfinityFree)
```
Host: sql307.infinityfree.com
Database: if0_39163993_cost_calculator
Username: if0_39163993
Password: sV5RSK88Jk5
```

## 🔧 Troubleshooting

### MySQL Connection Issues (Localhost)
1. **Check XAMPP/WAMP**: Ensure MySQL service is running
2. **Test phpMyAdmin**: Go to `http://localhost/phpmyadmin`
3. **Check credentials**: Default is usually `root` with empty password
4. **Port issues**: Make sure MySQL is running on port 3306

### Environment Not Switching
1. **Use switcher**: Go to `switch_environment.php`
2. **Check file permissions**: Ensure `config/environment.php` is writable
3. **Manual edit**: Edit `config/environment.php` directly

### Database Not Found
1. **Run setup**: Use `setup_localhost.php` for localhost
2. **Check database name**: Ensure it matches your configuration
3. **Create manually**: Use phpMyAdmin to create the database

## 📁 File Structure

```
/config/
  ├── environment.php     # Environment configuration
  └── db.php             # Database connection (uses environment.php)

/
├── setup_localhost.php   # Localhost setup script
├── switch_environment.php # Environment switcher
└── migrate_conditional_categories.php # Feature migration
```

## 🔄 Switching to Production

When ready to deploy to InfinityFree:

### Option 1: Automatic (Recommended)
1. **Upload files** to InfinityFree
2. **Keep environment.php** as `'auto'`
3. **System automatically detects** production environment

### Option 2: Manual
1. **Edit environment.php** before uploading
2. **Set** `$ENVIRONMENT = 'production';`
3. **Upload files** to InfinityFree

## 🆕 New Features Migration

After setting up localhost, run the migration for new features:
1. **Go to**: `http://localhost/your-project-folder/migrate_conditional_categories.php`
2. **Follow instructions** to add conditional category features
3. **Test new features** in admin panel

## 🔐 Default Login

```
Username: admin
Password: admin123
```

## 📋 Development Workflow

1. **Development**: Work on localhost with phpMyAdmin access
2. **Testing**: Test all features locally
3. **Migration**: Run migration scripts if needed
4. **Production**: Upload to InfinityFree (auto-switches environment)
5. **Verification**: Test on production environment

## 🎯 Quick Links

- **Localhost Setup**: `setup_localhost.php`
- **Environment Switcher**: `switch_environment.php`
- **phpMyAdmin**: `http://localhost/phpmyadmin`
- **Admin Dashboard**: `admin/dashboard.php`
- **Login Page**: `auth/login.php`
- **Migration**: `migrate_conditional_categories.php`

This setup gives you the flexibility to develop locally with full database access while easily deploying to production when ready!

<?php
// /ajax/get_recent_costs.php
session_start();
require_once __DIR__ . '/../config/db.php';
header('Content-Type: application/json');

try {
    // Get the 10 most recent costs
    $stmt = $pdo->prepare("
        SELECT id, description, amount, created_at 
        FROM costs 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $costs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'costs' => $costs
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error fetching costs: ' . $e->getMessage()
    ]);
}
?>

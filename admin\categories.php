<?php
// File: /admin/categories.php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// 1) Load config & auth
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../includes/auth.php';
authenticate_admin();

// 2) Fetch all categories
try {
    $stmt       = $pdo->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll();
} catch (PDOException $e) {
    $categories = [];
    $loadError  = $e->getMessage();
}

// 3) Render page
?>
<?php include __DIR__ . '/../includes/header.php'; ?>

<main class="container">

  <h1 class="section-title">
    <i class="fas fa-tags"></i> Category Management
  </h1>

  <?php if (!empty($loadError)): ?>
    <div class="alert alert-danger">
      Error loading categories: <?= htmlspecialchars($loadError) ?>
    </div>
  <?php endif; ?>

  <!-- Add New Category -->
  <section class="card mb-4">
    <div class="card-header">
      <h2><i class="fas fa-plus-circle"></i> Add New Category</h2>
    </div>
    <form id="category-form" action="javascript:;" class="card-body">
      <div class="form-group">
        <label for="category-name">Category Name</label>
        <input type="text"
               id="category-name"
               name="name"
               placeholder="Enter category name"
               required />
      </div>
      <div class="form-group">
        <label for="category-description">Description</label>
        <textarea id="category-description"
                  name="description"
                  placeholder="Enter description"></textarea>
      </div>
      <div class="form-group">
        <label class="checkbox-label">
          <input type="checkbox"
                 id="include-in-cost"
                 name="include_in_cost"
                 checked />
          <span class="checkmark"></span>
          Include this category in cost calculations
        </label>
        <small class="form-help">
          Uncheck this if the category should only be included when linked with a multiplier category
        </small>
      </div>
      <button type="submit" class="btn btn-primary">
        <i class="fas fa-plus"></i> Add Category
      </button>
    </form>
  </section>

  <!-- Existing Categories -->
  <h2 class="section-subtitle">
    <i class="fas fa-list"></i> Existing Categories
  </h2>

  <div class="dashboard-grid">
    <?php if (empty($categories)): ?>
      <div class="card">
        <div class="card-body">
          <p>No categories found. Add one above to get started.</p>
        </div>
      </div>
    <?php else: ?>
      <?php foreach ($categories as $cat): ?>
        <div class="card category-block" data-category-id="<?= $cat['id'] ?>">
          <div class="card-header">
            <h3>
              <?= htmlspecialchars($cat['name']) ?>
              <?php if (isset($cat['include_in_cost']) && !$cat['include_in_cost']): ?>
                <span class="badge badge-warning" title="Excluded from cost calculations unless linked">
                  <i class="fas fa-link"></i> Conditional
                </span>
              <?php endif; ?>
            </h3>
            <button class="delete-category-btn"
                    data-id="<?= $cat['id'] ?>"
                    title="Delete Category">
              <i class="fas fa-trash"></i>
            </button>
          </div>

          <?php if ($cat['description']): ?>
            <div class="card-body">
              <p><?= nl2br(htmlspecialchars($cat['description'])) ?></p>
              <?php if (isset($cat['include_in_cost']) && !$cat['include_in_cost']): ?>
                <p class="text-warning">
                  <i class="fas fa-info-circle"></i>
                  This category is excluded from cost calculations unless linked with a multiplier category.
                </p>
              <?php endif; ?>
            </div>
          <?php endif; ?>

          <div class="card-footer">
            <h4><i class="fas fa-layer-group"></i> Subcategories</h4>
            <ul id="sub-list-<?= $cat['id'] ?>" class="subcategory-list">
              <!-- populated by JS -->
            </ul>

            <form class="subcategory-form mt-3"
                  data-category-id="<?= $cat['id'] ?>"
                  action="javascript:;">
              <div class="form-group">
                <input type="text"
                       name="name"
                       placeholder="Subcategory name"
                       required />
              </div>
              <div class="form-group">
                <textarea name="description"
                          placeholder="Description"></textarea>
              </div>
              <button type="submit" class="btn btn-sm">
                <i class="fas fa-plus"></i> Add Subcategory
              </button>
            </form>
          </div>
        </div>
      <?php endforeach; ?>
    <?php endif; ?>
  </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle category form submission
    const categoryForm = document.getElementById('category-form');
    if (categoryForm) {
        categoryForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(categoryForm);
            const submitButton = categoryForm.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;

            // Show loading state
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
            submitButton.disabled = true;

            try {
                const response = await fetch('../ajax/save_category.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    alert('Category added successfully!');
                    categoryForm.reset();
                    // Reload the page to show the new category
                    window.location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                alert('Error: ' + error.message);
            } finally {
                // Restore button state
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }
        });
    }

    // Handle subcategory form submissions
    const subcategoryForms = document.querySelectorAll('.subcategory-form');
    subcategoryForms.forEach(form => {
        form.addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(form);
            const categoryId = form.dataset.categoryId;
            formData.append('category_id', categoryId);

            const submitButton = form.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;

            // Show loading state
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
            submitButton.disabled = true;

            try {
                const response = await fetch('../ajax/save_subcategory.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    alert('Subcategory added successfully!');
                    form.reset();
                    // Reload the page to show the new subcategory
                    window.location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                alert('Error: ' + error.message);
            } finally {
                // Restore button state
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }
        });
    });

    // Handle category deletion
    const deleteButtons = document.querySelectorAll('.delete-category-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', async function(e) {
            e.preventDefault();

            const categoryId = button.dataset.id;
            const categoryName = button.closest('.category-block').querySelector('h3').textContent.trim();

            if (!confirm(`Are you sure you want to delete the category "${categoryName}"? This will also delete all associated subcategories and costs.`)) {
                return;
            }

            try {
                const formData = new FormData();
                formData.append('id', categoryId);

                const response = await fetch('../ajax/delete_category.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    alert('Category deleted successfully!');
                    window.location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                alert('Error: ' + error.message);
            }
        });
    });

    // Load subcategories for each category
    function loadSubcategories(categoryId) {
        const subList = document.getElementById(`sub-list-${categoryId}`);
        if (!subList) return;

        subList.innerHTML = '<li class="loading">Loading subcategories...</li>';

        fetch(`../ajax/get_subcategories.php?category_id=${categoryId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.subcategories) {
                    if (data.subcategories.length === 0) {
                        subList.innerHTML = '<li class="empty-state">No subcategories yet</li>';
                    } else {
                        subList.innerHTML = data.subcategories.map(sub => `
                            <li class="subcategory-item" data-subcategory-id="${sub.id}">
                                <div class="subcategory-content">
                                    <strong>${escapeHtml(sub.name)}</strong>
                                    ${sub.description ? `<br><small>${escapeHtml(sub.description)}</small>` : ''}
                                </div>
                                <button class="delete-subcategory-btn"
                                        data-id="${sub.id}"
                                        data-category-id="${categoryId}"
                                        title="Delete Subcategory">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </li>
                        `).join('');
                    }
                } else {
                    subList.innerHTML = '<li class="error-state">Error loading subcategories</li>';
                }
            })
            .catch(error => {
                console.error('Error loading subcategories:', error);
                subList.innerHTML = '<li class="error-state">Failed to load subcategories</li>';
            });
    }

    // Helper function to escape HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Load subcategories for all categories on page load
    const categoryBlocks = document.querySelectorAll('.category-block');
    categoryBlocks.forEach(block => {
        const categoryId = block.dataset.categoryId;
        if (categoryId) {
            loadSubcategories(categoryId);
        }
    });

    // Handle subcategory deletion
    document.addEventListener('click', async function(e) {
        if (e.target.closest('.delete-subcategory-btn')) {
            e.preventDefault();

            const button = e.target.closest('.delete-subcategory-btn');
            const subcategoryId = button.dataset.id;
            const categoryId = button.dataset.categoryId;
            const subcategoryItem = button.closest('.subcategory-item');
            const subcategoryName = subcategoryItem.querySelector('strong').textContent;

            if (!confirm(`Are you sure you want to delete the subcategory "${subcategoryName}"?`)) {
                return;
            }

            try {
                const formData = new FormData();
                formData.append('id', subcategoryId);

                const response = await fetch('../ajax/delete_subcategory.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    alert('Subcategory deleted successfully!');
                    // Reload subcategories for this category
                    loadSubcategories(categoryId);
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }
    });

    // Reload subcategories after adding a new one
    const originalSubcategoryForms = document.querySelectorAll('.subcategory-form');
    originalSubcategoryForms.forEach(form => {
        form.addEventListener('submit', function() {
            const categoryId = form.dataset.categoryId;
            // Add a delay to allow the subcategory to be saved first
            setTimeout(() => {
                loadSubcategories(categoryId);
            }, 1500);
        });
    });
});
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?>

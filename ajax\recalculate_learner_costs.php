<?php
// /ajax/recalculate_learner_costs.php
require_once __DIR__ . '/../config/db.php';
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get the current total learners
        $stmt = $pdo->query("SELECT SUM(total_students) as total FROM revenue");
        $result = $stmt->fetch();
        $currentTotalLearners = intval($result['total'] ?? 0);
        
        if ($currentTotalLearners <= 0) {
            echo json_encode([
                'success' => false,
                'message' => 'No learners found in revenue data. Cannot recalculate costs.',
                'updated_costs' => 0
            ]);
            exit;
        }
        
        // Find all costs that are multiplied by learners
        $findCostsStmt = $pdo->prepare("
            SELECT id, description, original_amount_per_learner, learner_count_used, num_days
            FROM costs 
            WHERE is_multiplied_by_learners = TRUE 
            AND original_amount_per_learner IS NOT NULL 
            AND learner_count_used IS NOT NULL
        ");
        $findCostsStmt->execute();
        $learnerCosts = $findCostsStmt->fetchAll();
        
        $updatedCount = 0;
        $updateDetails = [];
        
        // Begin transaction for data consistency
        $pdo->beginTransaction();
        
        foreach ($learnerCosts as $cost) {
            $oldAmount = $cost['original_amount_per_learner'] * $cost['num_days'] * $cost['learner_count_used'];
            $newAmount = $cost['original_amount_per_learner'] * $cost['num_days'] * $currentTotalLearners;
            
            // Only update if the amount actually changes
            if ($oldAmount != $newAmount) {
                $updateStmt = $pdo->prepare("
                    UPDATE costs 
                    SET amount = ?, 
                        learner_count_used = ?, 
                        last_learner_update = NOW() 
                    WHERE id = ?
                ");
                $updateStmt->execute([$newAmount, $currentTotalLearners, $cost['id']]);
                
                $updatedCount++;
                $updateDetails[] = [
                    'id' => $cost['id'],
                    'description' => $cost['description'],
                    'old_amount' => $oldAmount,
                    'new_amount' => $newAmount,
                    'old_learner_count' => $cost['learner_count_used'],
                    'new_learner_count' => $currentTotalLearners
                ];
            }
        }
        
        // Commit the transaction
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => "Successfully recalculated {$updatedCount} cost(s) for {$currentTotalLearners} learners.",
            'updated_costs' => $updatedCount,
            'current_learner_count' => $currentTotalLearners,
            'total_learner_costs_found' => count($learnerCosts),
            'update_details' => $updateDetails
        ]);
        
    } catch (Exception $e) {
        // Rollback transaction on error
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        
        error_log("Error recalculating learner costs: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => 'Error recalculating learner costs: ' . $e->getMessage(),
            'updated_costs' => 0
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method',
        'updated_costs' => 0
    ]);
}
?>

<?php
/**
 * Debug page to troubleshoot costs display issues
 */
require_once __DIR__ . '/config/db.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Debug Costs - Troubleshooting</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; padding: 10px; background: #e8f5e8; border-radius: 5px; margin: 10px 0; }
        .error { color: red; padding: 10px; background: #ffe8e8; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; padding: 10px; background: #e8f0ff; border-radius: 5px; margin: 10px 0; }
        .warning { color: orange; padding: 10px; background: #fff8e8; border-radius: 5px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .code { background: #f5f5f5; padding: 10px; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body>";

echo "<h1>🔍 Debug Costs - Troubleshooting</h1>";

try {
    // 1. Check database connection
    echo "<div class='info'>✅ Database connection successful</div>";
    
    // 2. Check if costs table exists
    $tablesCheck = $pdo->query("SHOW TABLES LIKE 'costs'");
    $costsTableExists = $tablesCheck->fetch();
    
    if ($costsTableExists) {
        echo "<div class='success'>✅ Costs table exists</div>";
        
        // 3. Show costs table structure
        echo "<h2>📋 Costs Table Structure</h2>";
        echo "<table>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        $columns = $pdo->query("SHOW COLUMNS FROM costs");
        $columnNames = [];
        while ($column = $columns->fetch()) {
            $columnNames[] = $column['Field'];
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 4. Check for new learner multiplier columns
        $newColumns = ['is_multiplied_by_learners', 'original_amount_per_learner', 'learner_count_used', 'last_learner_update'];
        $missingColumns = [];
        foreach ($newColumns as $col) {
            if (!in_array($col, $columnNames)) {
                $missingColumns[] = $col;
            }
        }
        
        if (empty($missingColumns)) {
            echo "<div class='success'>✅ All learner multiplier columns exist</div>";
        } else {
            echo "<div class='warning'>⚠️ Missing learner multiplier columns: " . implode(', ', $missingColumns) . "</div>";
            echo "<div class='info'>💡 You need to run the database migration: <code>add_learner_multiplier_fields.php</code></div>";
        }
        
        // 5. Count total costs
        $countResult = $pdo->query("SELECT COUNT(*) as total FROM costs");
        $totalCosts = $countResult->fetch()['total'];
        
        echo "<div class='info'>📊 Total costs in database: <strong>{$totalCosts}</strong></div>";
        
        if ($totalCosts > 0) {
            // 6. Show sample costs data
            echo "<h2>📝 Sample Costs Data (First 5 records)</h2>";
            
            // Use basic query that works with or without new columns
            $sampleCosts = $pdo->query("
                SELECT co.*, ca.name AS category, su.name AS subcategory
                FROM costs co 
                LEFT JOIN categories ca ON co.category_id = ca.id 
                LEFT JOIN subcategories su ON co.subcategory_id = su.id 
                ORDER BY co.created_at DESC 
                LIMIT 5
            ")->fetchAll();
            
            if (!empty($sampleCosts)) {
                echo "<table>";
                echo "<tr><th>ID</th><th>Description</th><th>Amount</th><th>Category</th><th>Subcategory</th><th>Created</th></tr>";
                foreach ($sampleCosts as $cost) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($cost['id']) . "</td>";
                    echo "<td>" . htmlspecialchars($cost['description']) . "</td>";
                    echo "<td>R" . number_format($cost['amount'], 2) . "</td>";
                    echo "<td>" . htmlspecialchars($cost['category'] ?? 'N/A') . "</td>";
                    echo "<td>" . htmlspecialchars($cost['subcategory'] ?? 'N/A') . "</td>";
                    echo "<td>" . htmlspecialchars($cost['created_at']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<div class='warning'>⚠️ No costs found in query result</div>";
            }
            
            // 7. Check categories and subcategories
            echo "<h2>🏷️ Categories and Subcategories</h2>";
            
            $categoriesCount = $pdo->query("SELECT COUNT(*) as total FROM categories")->fetch()['total'];
            $subcategoriesCount = $pdo->query("SELECT COUNT(*) as total FROM subcategories")->fetch()['total'];
            
            echo "<div class='info'>Categories: <strong>{$categoriesCount}</strong> | Subcategories: <strong>{$subcategoriesCount}</strong></div>";
            
            if ($categoriesCount == 0) {
                echo "<div class='warning'>⚠️ No categories found. This might prevent costs from displaying properly.</div>";
            }
            
        } else {
            echo "<div class='warning'>⚠️ No costs found in the database</div>";
            echo "<div class='info'>💡 Try adding a cost using the form on the costs page</div>";
        }
        
        // 8. Check revenue data (for learner count)
        echo "<h2>👥 Revenue Data (Learner Count)</h2>";
        
        $revenueCount = $pdo->query("SELECT COUNT(*) as total FROM revenue")->fetch()['total'];
        $totalLearners = $pdo->query("SELECT SUM(total_students) as total FROM revenue")->fetch()['total'] ?? 0;
        
        echo "<div class='info'>Revenue records: <strong>{$revenueCount}</strong> | Total learners: <strong>{$totalLearners}</strong></div>";
        
        if ($revenueCount == 0) {
            echo "<div class='warning'>⚠️ No revenue data found. Add some revenue data to enable learner multiplier functionality.</div>";
        }
        
    } else {
        echo "<div class='error'>❌ Costs table does not exist!</div>";
        echo "<div class='info'>💡 Make sure your database is properly set up</div>";
    }
    
    // 9. Test the costs query that the page uses
    echo "<h2>🧪 Test Costs Query</h2>";
    
    try {
        // Test the exact query from costs.php
        $columnsCheck = $pdo->query("SHOW COLUMNS FROM costs LIKE 'is_multiplied_by_learners'");
        $newColumnsExist = $columnsCheck->fetch();
        
        if ($newColumnsExist) {
            echo "<div class='info'>Testing query with learner multiplier columns...</div>";
            $testQuery = "
                SELECT co.*, ca.name AS category, su.name AS subcategory,
                       co.is_multiplied_by_learners, co.original_amount_per_learner, 
                       co.learner_count_used, co.last_learner_update
                FROM costs co 
                LEFT JOIN categories ca ON co.category_id = ca.id 
                LEFT JOIN subcategories su ON co.subcategory_id = su.id 
                ORDER BY ca.name, su.name, co.created_at DESC
                LIMIT 1
            ";
        } else {
            echo "<div class='info'>Testing query without learner multiplier columns...</div>";
            $testQuery = "
                SELECT co.*, ca.name AS category, su.name AS subcategory
                FROM costs co 
                LEFT JOIN categories ca ON co.category_id = ca.id 
                LEFT JOIN subcategories su ON co.subcategory_id = su.id 
                ORDER BY ca.name, su.name, co.created_at DESC
                LIMIT 1
            ";
        }
        
        $testResult = $pdo->query($testQuery);
        $testCost = $testResult->fetch();
        
        if ($testCost) {
            echo "<div class='success'>✅ Query executed successfully</div>";
            echo "<div class='code'>Sample result: " . json_encode($testCost, JSON_PRETTY_PRINT) . "</div>";
        } else {
            echo "<div class='warning'>⚠️ Query executed but returned no results</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Query failed: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<h2>🔧 Next Steps</h2>";
echo "<ul>";
echo "<li>If you see missing learner multiplier columns, run: <code>add_learner_multiplier_fields.php</code></li>";
echo "<li>If no costs are found, try adding a cost using the form</li>";
echo "<li>If no categories exist, add some categories first</li>";
echo "<li>Check the browser console for JavaScript errors</li>";
echo "</ul>";

echo "</body></html>";
?>

<?php
// /test_environment.php
// Quick test to verify environment detection

require_once __DIR__ . '/config/environment.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Environment Test - Cost Calculator</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
        .env-development { border-left: 4px solid #28a745; }
        .env-production { border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <h1>🔍 Environment Detection Test</h1>
    
    <div class="info env-<?= getCurrentEnvironment() ?>">
        <h3>Current Environment: <?= strtoupper(getCurrentEnvironment()) ?></h3>
        <p>Environment detection is working correctly!</p>
    </div>

    <h2>📊 Server Information</h2>
    <table>
        <tr>
            <th>Variable</th>
            <th>Value</th>
        </tr>
        <tr>
            <td>SERVER_NAME</td>
            <td><?= htmlspecialchars($_SERVER['SERVER_NAME'] ?? 'Not set') ?></td>
        </tr>
        <tr>
            <td>HTTP_HOST</td>
            <td><?= htmlspecialchars($_SERVER['HTTP_HOST'] ?? 'Not set') ?></td>
        </tr>
        <tr>
            <td>REQUEST_URI</td>
            <td><?= htmlspecialchars($_SERVER['REQUEST_URI'] ?? 'Not set') ?></td>
        </tr>
        <tr>
            <td>DOCUMENT_ROOT</td>
            <td><?= htmlspecialchars($_SERVER['DOCUMENT_ROOT'] ?? 'Not set') ?></td>
        </tr>
    </table>

    <h2>🗄️ Database Configuration</h2>
    <table>
        <tr>
            <th>Setting</th>
            <th>Value</th>
        </tr>
        <tr>
            <td>Environment</td>
            <td><strong><?= getCurrentEnvironment() ?></strong></td>
        </tr>
        <tr>
            <td>Host</td>
            <td><?= htmlspecialchars($host) ?></td>
        </tr>
        <tr>
            <td>Database</td>
            <td><?= htmlspecialchars($db) ?></td>
        </tr>
        <tr>
            <td>Username</td>
            <td><?= htmlspecialchars($user) ?></td>
        </tr>
        <tr>
            <td>Password</td>
            <td><?= empty($pass) ? '(empty)' : '***hidden***' ?></td>
        </tr>
        <tr>
            <td>Charset</td>
            <td><?= htmlspecialchars($charset) ?></td>
        </tr>
        <tr>
            <td>Debug Mode</td>
            <td><?= $debug ? 'Enabled' : 'Disabled' ?></td>
        </tr>
    </table>

    <h2>🔧 Connection Test</h2>
    <?php
    try {
        $testPdo = new PDO("mysql:host=$host;charset=$charset", $user, $pass);
        $testPdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<div class='success'>✅ MySQL connection successful!</div>";
        
        // Test database selection
        try {
            $testPdo->exec("USE `$db`");
            echo "<div class='success'>✅ Database '$db' exists and accessible!</div>";
        } catch (PDOException $e) {
            if (getCurrentEnvironment() === 'development') {
                echo "<div class='warning'>⚠️ Database '$db' doesn't exist yet. This is normal for localhost - it will be created during setup.</div>";
            } else {
                echo "<div class='warning'>⚠️ Database '$db' not found. Please create it in your hosting control panel.</div>";
            }
        }
        
    } catch (PDOException $e) {
        echo "<div class='warning'>❌ Connection failed: " . htmlspecialchars($e->getMessage()) . "</div>";
        
        if (getCurrentEnvironment() === 'development') {
            echo "<div class='info'>
            <h4>💡 Localhost Troubleshooting:</h4>
            <ul>
                <li>Make sure XAMPP/WAMP is running</li>
                <li>Check that MySQL service is started</li>
                <li>Test phpMyAdmin: <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></li>
                <li>Default credentials are usually: root / (empty password)</li>
            </ul>
            </div>";
        }
    }
    ?>

    <h2>🎯 Next Steps</h2>
    <div class="info">
        <?php if (getCurrentEnvironment() === 'development'): ?>
        <h4>For Localhost Development:</h4>
        <ul>
            <li><a href="setup_localhost.php">🚀 Run Localhost Setup</a> - Automated setup for development</li>
            <li><a href="http://localhost/phpmyadmin" target="_blank">📊 Open phpMyAdmin</a> - Database management</li>
            <li><a href="switch_environment.php">🔄 Environment Switcher</a> - Change environments</li>
        </ul>
        <?php else: ?>
        <h4>For Production (InfinityFree):</h4>
        <ul>
            <li><a href="setup.php">🚀 Run Setup</a> - Create tables and initial data</li>
            <li>Make sure database exists in InfinityFree control panel</li>
            <li><a href="switch_environment.php">🔄 Environment Switcher</a> - Change environments</li>
        </ul>
        <?php endif; ?>
        
        <h4>General:</h4>
        <ul>
            <li><a href="migrate_conditional_categories.php">🔄 Run Migration</a> - Add new features</li>
            <li><a href="auth/login.php">🔐 Login</a> - Access the application (admin/admin123)</li>
        </ul>
    </div>

    <h2>📝 Environment Configuration</h2>
    <div class="info">
        <p>Environment setting in <code>config/environment.php</code>:</p>
        <ul>
            <li><strong>Current:</strong> <?= isset($ENVIRONMENT) ? $ENVIRONMENT : 'auto' ?></li>
            <li><strong>Auto-detect:</strong> <?= getCurrentEnvironment() ?></li>
        </ul>
        <p>To change environment manually, edit <code>config/environment.php</code> or use the <a href="switch_environment.php">Environment Switcher</a>.</p>
    </div>
</body>
</html>

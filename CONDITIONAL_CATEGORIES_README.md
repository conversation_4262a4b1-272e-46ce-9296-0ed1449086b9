# Conditional Category Inclusion System

## Overview

This system enhancement allows certain categories to be optionally excluded from cost calculations unless they are linked with another category that adds a multiplier to their entire cost. This enables conditional inclusion of a category's total cost, influenced by other categories.

## Features

### 1. Category Creation with Conditional Inclusion
- **New Field**: `include_in_cost` (<PERSON>olean, default: TRUE)
- **UI**: Checkbox in category creation form: "Include this category in cost calculations?"
- **Behavior**: When unchecked, the category is excluded from cost totals by default

### 2. Category Linking System
- **Purpose**: Create multiplier relationships between categories
- **Components**:
  - **Multiplier Category**: Category that triggers the multiplication (must have `include_in_cost = TRUE`)
  - **Target Category**: Category that gets multiplied (must have `include_in_cost = FALSE`)
  - **Multiplier**: Factor applied to target category's total cost

### 3. Enhanced Cost Calculation Logic
- **Standard Categories**: Included in cost calculations as normal
- **Excluded Categories**: Only included when linked via multiplier relationships
- **Multiplier Logic**: `total_cost += (target_category_total) * multiplier`

## Database Schema

### Modified Tables

#### `categories` Table
```sql
ALTER TABLE categories ADD COLUMN include_in_cost BOOLEAN DEFAULT TRUE;
```

#### New `category_links` Table
```sql
CREATE TABLE category_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    multiplier_category_id INT NOT NULL,
    target_category_id INT NOT NULL,
    multiplier DECIMAL(10,2) NOT NULL DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (multiplier_category_id) REFERENCES categories(id) ON DELETE CASCADE,
    FOREIGN KEY (target_category_id) REFERENCES categories(id) ON DELETE CASCADE,
    UNIQUE KEY unique_link (multiplier_category_id, target_category_id)
);
```

## Usage Example

### Scenario: Weekend Work Uplift

1. **Create Categories**:
   - "Labor Costs" with `include_in_cost = FALSE`
   - "Weekend Work Uplift" with `include_in_cost = TRUE`

2. **Add Costs**:
   - Add various labor costs to "Labor Costs" category
   - Add any costs to "Weekend Work Uplift" category

3. **Create Link**:
   - Link "Weekend Work Uplift" → "Labor Costs" with multiplier 1.5

4. **Result**:
   - When "Weekend Work Uplift" has costs, it includes all "Labor Costs" × 1.5
   - When "Weekend Work Uplift" has no costs, "Labor Costs" are excluded from totals

## File Structure

### New Files
- `admin/category_links.php` - Category links management interface
- `ajax/save_category_link.php` - Create category links
- `ajax/get_category_links.php` - Retrieve category links
- `ajax/delete_category_link.php` - Delete category links
- `includes/cost_calculator.php` - Enhanced cost calculation logic
- `migrate_conditional_categories.php` - Database migration script

### Modified Files
- `admin/categories.php` - Added conditional inclusion checkbox
- `ajax/save_category.php` - Handle include_in_cost field
- `viewer/viewer_dashboard.php` - Use new cost calculation logic
- `admin/analytics.php` - Use new cost calculation logic
- `ajax/get_analytics_data.php` - Use new cost calculation logic
- `ajax/get_analytics.php` - Use new cost calculation logic
- `includes/header.php` - Added Category Links navigation
- `assets/css/style.css` - Added badge styles for conditional categories
- Database setup files - Added new schema elements

## Installation

### For New Installations
The new schema is automatically included in all setup files.

### For Existing Installations
Run the migration script:
```bash
php migrate_conditional_categories.php
```

## API Reference

### Cost Calculation Functions

#### `calculateTotalCosts($pdo, $dateFilter = null)`
- **Purpose**: Calculate total costs with conditional inclusion logic
- **Parameters**: 
  - `$pdo`: Database connection
  - `$dateFilter`: Optional date filter (YYYY-MM format)
- **Returns**: Float - Total calculated cost

#### `getCostBreakdownByCategory($pdo, $dateFilter = null)`
- **Purpose**: Get cost breakdown respecting conditional inclusion
- **Returns**: Array of categories with calculated costs

### AJAX Endpoints

#### `ajax/save_category_link.php`
- **Method**: POST
- **Parameters**: `multiplier_category_id`, `target_category_id`, `multiplier`
- **Response**: JSON with success/error status

#### `ajax/get_category_links.php`
- **Method**: GET
- **Response**: JSON array of category links with names

#### `ajax/delete_category_link.php`
- **Method**: POST
- **Parameters**: `link_id`
- **Response**: JSON with success/error status

## Validation Rules

1. **Target Category**: Must have `include_in_cost = FALSE`
2. **Multiplier Category**: Must have `include_in_cost = TRUE`
3. **Self-Linking**: Categories cannot link to themselves
4. **Unique Links**: Only one link per multiplier-target pair
5. **Multiplier Value**: Must be greater than zero

## UI Indicators

- **Conditional Badge**: Categories with `include_in_cost = FALSE` show a "Conditional" badge
- **Warning Text**: Explanatory text for excluded categories
- **Link Management**: Dedicated interface for managing category relationships

## Cost Calculation Flow

1. **Standard Costs**: Sum all costs from categories with `include_in_cost = TRUE`
2. **Multiplier Costs**: For each category link:
   - Check if multiplier category has any costs
   - If yes, calculate: `target_category_total * multiplier`
   - Add to total costs
3. **Final Total**: Sum of standard costs + all multiplier costs

This system provides flexible cost modeling for scenarios where certain cost categories should only be included under specific conditions.

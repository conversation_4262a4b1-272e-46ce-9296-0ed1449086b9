<?php
// /switch_environment.php
// Quick environment switcher

$envFile = __DIR__ . '/config/environment.php';

// Handle form submission
if ($_POST['action'] ?? false) {
    $newEnvironment = $_POST['environment'] ?? 'auto';
    
    // Read current file
    $content = file_get_contents($envFile);
    
    // Replace the environment setting
    $pattern = '/\$ENVIRONMENT\s*=\s*[\'"][^\'"]*[\'"];/';
    $replacement = "\$ENVIRONMENT = '$newEnvironment';";
    $newContent = preg_replace($pattern, $replacement, $content);
    
    // Write back to file
    if (file_put_contents($envFile, $newContent)) {
        $message = "✅ Environment switched to: $newEnvironment";
        $messageType = 'success';
    } else {
        $message = "❌ Failed to update environment file";
        $messageType = 'error';
    }
}

// Get current environment setting
$content = file_get_contents($envFile);
preg_match('/\$ENVIRONMENT\s*=\s*[\'"]([^\'"]*)[\'"];/', $content, $matches);
$currentSetting = $matches[1] ?? 'auto';

// Load environment to see what's actually being used
require_once __DIR__ . '/config/environment.php';
$actualEnvironment = getCurrentEnvironment();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Environment Switcher - Cost Calculator</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        select, button { padding: 10px; font-size: 16px; border-radius: 5px; border: 1px solid #ccc; }
        button { background: #007bff; color: white; cursor: pointer; }
        button:hover { background: #0056b3; }
        .current { background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
        .env-development { border-left-color: #28a745; }
        .env-production { border-left-color: #dc3545; }
    </style>
</head>
<body>
    <h1>🔄 Environment Switcher</h1>
    
    <?php if (isset($message)): ?>
        <div class="<?= $messageType ?>">
            <?= htmlspecialchars($message) ?>
        </div>
    <?php endif; ?>
    
    <div class="current env-<?= $actualEnvironment ?>">
        <h3>Current Status</h3>
        <p><strong>Environment Setting:</strong> <?= htmlspecialchars($currentSetting) ?></p>
        <p><strong>Actual Environment:</strong> <?= htmlspecialchars($actualEnvironment) ?></p>
        <p><strong>Database Host:</strong> <?= htmlspecialchars($host) ?></p>
        <p><strong>Database Name:</strong> <?= htmlspecialchars($db) ?></p>
    </div>
    
    <form method="POST">
        <div class="form-group">
            <label for="environment">Switch Environment:</label>
            <select name="environment" id="environment">
                <option value="auto" <?= $currentSetting === 'auto' ? 'selected' : '' ?>>
                    Auto-detect (Recommended)
                </option>
                <option value="development" <?= $currentSetting === 'development' ? 'selected' : '' ?>>
                    Development (Localhost)
                </option>
                <option value="production" <?= $currentSetting === 'production' ? 'selected' : '' ?>>
                    Production (InfinityFree)
                </option>
            </select>
        </div>
        
        <button type="submit" name="action" value="switch">
            Switch Environment
        </button>
    </form>
    
    <div class="info">
        <h3>Environment Descriptions</h3>
        <ul>
            <li><strong>Auto-detect:</strong> Automatically detects localhost vs production based on server name</li>
            <li><strong>Development:</strong> Forces localhost settings (MySQL: localhost, root, no password)</li>
            <li><strong>Production:</strong> Forces InfinityFree settings</li>
        </ul>
    </div>
    
    <h3>Quick Links</h3>
    <ul>
        <li><a href="setup_localhost.php">🚀 Localhost Setup</a></li>
        <li><a href="admin/dashboard.php">🏠 Admin Dashboard</a></li>
        <li><a href="http://localhost/phpmyadmin" target="_blank">📊 phpMyAdmin</a> (localhost only)</li>
        <li><a href="migrate_conditional_categories.php">🔄 Run Migration</a></li>
    </ul>
</body>
</html>

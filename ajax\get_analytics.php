<?php
// /ajax/get_analytics.php
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../includes/cost_calculator.php';
header('Content-Type: application/json');

$data = [
    'monthly' => [],
    'categories' => [],
    'subcategories' => [],
    'studentTypes' => [],
    'summary' => [],
    'trends' => [],
    'profitability' => []
];

// Monthly Revenue vs Costs
$monthlyStmt = $pdo->query("SELECT month_year, SUM(revenue_per_student * total_students) AS revenue FROM revenue GROUP BY month_year ORDER BY month_year");
while ($row = $monthlyStmt->fetch()) {
    $month = $row['month_year'];
    $data['monthly'][$month] = ['revenue' => floatval($row['revenue']), 'cost' => 0, 'profit' => 0];
}

// Get costs by month using new calculation logic
$monthsStmt = $pdo->query("SELECT DISTINCT DATE_FORMAT(created_at, '%Y-%m') AS month_year FROM costs ORDER BY month_year");
while ($row = $monthsStmt->fetch()) {
    $month = $row['month_year'];
    if (!isset($data['monthly'][$month])) {
        $data['monthly'][$month] = ['revenue' => 0, 'cost' => 0, 'profit' => 0];
    }
    $data['monthly'][$month]['cost'] = calculateTotalCosts($pdo, $month);
    // Calculate profit
    $data['monthly'][$month]['profit'] = $data['monthly'][$month]['revenue'] - $data['monthly'][$month]['cost'];
}

// Cost Breakdown by Category using new calculation logic
$categoryBreakdown = getCostBreakdownByCategory($pdo);
foreach ($categoryBreakdown as $cat) {
    $data['categories'][] = [
        'id' => 0, // Not applicable for multiplier categories
        'label' => $cat['category'],
        'value' => floatval($cat['total'])
    ];
}

// Cost Breakdown by Subcategory
$subStmt = $pdo->query("
    SELECT
        c.name AS category_name,
        s.name AS subcategory_name,
        SUM(co.amount * IF(co.rate_type='daily', co.num_days, 1)) AS total
    FROM costs co
    JOIN categories c ON co.category_id = c.id
    JOIN subcategories s ON co.subcategory_id = s.id
    GROUP BY c.name, s.name
    ORDER BY c.name, total DESC
");
while ($row = $subStmt->fetch()) {
    $data['subcategories'][] = [
        'category' => $row['category_name'],
        'label' => $row['subcategory_name'],
        'value' => floatval($row['total'])
    ];
}

// Revenue by Student Type
$studentStmt = $pdo->query("SELECT student_type, SUM(revenue_per_student * total_students) AS total FROM revenue GROUP BY student_type");
while ($row = $studentStmt->fetch()) {
    $data['studentTypes'][] = [
        'label' => $row['student_type'],
        'value' => floatval($row['total'])
    ];
}

// Summary Statistics
$summaryStmt = $pdo->query("
    SELECT
        (SELECT SUM(revenue_per_student * total_students) FROM revenue) AS total_revenue,
        (SELECT COUNT(*) FROM costs) AS cost_count,
        (SELECT COUNT(*) FROM revenue) AS revenue_count,
        (SELECT COUNT(*) FROM invoices) AS invoice_count,
        (SELECT AVG(amount * IF(rate_type='daily', num_days, 1)) FROM costs) AS avg_cost,
        (SELECT AVG(revenue_per_student * total_students) FROM revenue) AS avg_revenue_per_month
");
$summary = $summaryStmt->fetch();
$data['summary'] = [
    'total_costs' => calculateTotalCosts($pdo),
    'total_revenue' => floatval($summary['total_revenue'] ?? 0),
    'total_profit' => floatval(($summary['total_revenue'] ?? 0) - ($summary['total_costs'] ?? 0)),
    'cost_count' => intval($summary['cost_count'] ?? 0),
    'revenue_count' => intval($summary['revenue_count'] ?? 0),
    'invoice_count' => intval($summary['invoice_count'] ?? 0),
    'avg_cost' => floatval($summary['avg_cost'] ?? 0),
    'avg_revenue_per_month' => floatval($summary['avg_revenue_per_month'] ?? 0),
    'profit_margin' => ($summary['total_revenue'] > 0) ?
        floatval((($summary['total_revenue'] - $summary['total_costs']) / $summary['total_revenue']) * 100) : 0
];

// Trend Analysis - Last 6 months
$trendStmt = $pdo->query("
    SELECT
        DATE_FORMAT(created_at, '%Y-%m') AS month,
        SUM(amount * IF(rate_type='daily', num_days, 1)) AS monthly_cost
    FROM costs
    WHERE created_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 6 MONTH)
    GROUP BY month
    ORDER BY month
");
while ($row = $trendStmt->fetch()) {
    $data['trends']['costs'][] = [
        'month' => $row['month'],
        'value' => floatval($row['monthly_cost'])
    ];
}

$revTrendStmt = $pdo->query("
    SELECT
        month_year AS month,
        SUM(revenue_per_student * total_students) AS monthly_revenue
    FROM revenue
    WHERE STR_TO_DATE(CONCAT(month_year, '-01'), '%Y-%m-%d') >= DATE_SUB(CURRENT_DATE(), INTERVAL 6 MONTH)
    GROUP BY month
    ORDER BY month
");
while ($row = $revTrendStmt->fetch()) {
    $data['trends']['revenue'][] = [
        'month' => $row['month'],
        'value' => floatval($row['monthly_revenue'])
    ];
}

// Profitability Analysis by Month
foreach ($data['monthly'] as $month => $values) {
    $profit = $values['revenue'] - $values['cost'];
    $profitMargin = ($values['revenue'] > 0) ? (($profit / $values['revenue']) * 100) : 0;

    $data['profitability'][] = [
        'month' => $month,
        'revenue' => $values['revenue'],
        'cost' => $values['cost'],
        'profit' => $profit,
        'margin' => $profitMargin
    ];
}

// Sort profitability by month
usort($data['profitability'], function($a, $b) {
    return strcmp($a['month'], $b['month']);
});

echo json_encode($data);
?>
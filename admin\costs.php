<?php
// /admin/costs.php
require_once __DIR__ . '/../includes/header.php';

// Role-based guard for admin
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
  header('Location: ../auth/login.php');
  exit;
}

// Fetch costs data with error handling, including learner multiplier fields if they exist
try {
    // First check if the new columns exist
    $columnsCheck = $pdo->query("SHOW COLUMNS FROM costs LIKE 'is_multiplied_by_learners'");
    $newColumnsExist = $columnsCheck->fetch();

    if ($newColumnsExist) {
        // New columns exist, use the full query
        $costs = $pdo->query("
            SELECT co.*, ca.name AS category, su.name AS subcategory,
                   co.is_multiplied_by_learners, co.original_amount_per_learner,
                   co.learner_count_used, co.last_learner_update
            FROM costs co
            LEFT JOIN categories ca ON co.category_id = ca.id
            LEFT JOIN subcategories su ON co.subcategory_id = su.id
            ORDER BY ca.name, su.name, co.created_at DESC
        ")->fetchAll();
    } else {
        // New columns don't exist yet, use basic query and add default values
        $costsRaw = $pdo->query("
            SELECT co.*, ca.name AS category, su.name AS subcategory
            FROM costs co
            LEFT JOIN categories ca ON co.category_id = ca.id
            LEFT JOIN subcategories su ON co.subcategory_id = su.id
            ORDER BY ca.name, su.name, co.created_at DESC
        ")->fetchAll();

        // Add default values for new fields
        $costs = [];
        foreach ($costsRaw as $cost) {
            $cost['is_multiplied_by_learners'] = false;
            $cost['original_amount_per_learner'] = null;
            $cost['learner_count_used'] = null;
            $cost['last_learner_update'] = null;
            $costs[] = $cost;
        }
    }
} catch (Exception $e) {
    $costs = [];
    error_log("Error fetching costs: " . $e->getMessage());
}

// Get total number of learners from revenue data
try {
    $totalLearners = $pdo->query("SELECT SUM(total_students) as total FROM revenue")->fetch()['total'] ?? 0;
} catch (Exception $e) {
    $totalLearners = 0;
}

// Get category links to show relationships
$categoryLinks = [];
try {
    $linksQuery = $pdo->query("
        SELECT
            cl.multiplier_category_id,
            cl.target_category_id,
            cl.multiplier,
            mc.name as multiplier_category_name,
            tc.name as target_category_name
        FROM category_links cl
        LEFT JOIN categories mc ON cl.multiplier_category_id = mc.id
        LEFT JOIN categories tc ON cl.target_category_id = tc.id
        ORDER BY mc.name, tc.name
    ");
    $categoryLinks = $linksQuery->fetchAll();
} catch (Exception $e) {
    $categoryLinks = [];
}

// Group costs by category and subcategory, including linked category information
$groupedCosts = [];
$categoryInfo = []; // Store category metadata

foreach ($costs as $cost) {
    $category = $cost['category'] ?? 'Uncategorized';
    $subcategory = $cost['subcategory'] ?? 'No Subcategory';
    $categoryId = $cost['category_id'];

    if (!isset($groupedCosts[$category])) {
        $groupedCosts[$category] = [];

        // Store category info including links
        $categoryInfo[$category] = [
            'id' => $categoryId,
            'linked_to' => [],
            'linked_from' => [],
            'total_with_links' => 0
        ];

        // Find what this category is linked to (as multiplier)
        foreach ($categoryLinks as $link) {
            if ($link['multiplier_category_id'] == $categoryId) {
                $categoryInfo[$category]['linked_to'][] = [
                    'target_category' => $link['target_category_name'],
                    'target_id' => $link['target_category_id'],
                    'multiplier' => $link['multiplier']
                ];
            }

            // Find what categories link to this one (as target)
            if ($link['target_category_id'] == $categoryId) {
                $categoryInfo[$category]['linked_from'][] = [
                    'multiplier_category' => $link['multiplier_category_name'],
                    'multiplier_id' => $link['multiplier_category_id'],
                    'multiplier' => $link['multiplier']
                ];
            }
        }
    }

    if (!isset($groupedCosts[$category][$subcategory])) {
        $groupedCosts[$category][$subcategory] = [];
    }

    $groupedCosts[$category][$subcategory][] = $cost;
}

// Function to calculate linked category costs
function calculateLinkedCategoryCosts($pdo, $categoryId, $categoryLinks) {
    $linkedCosts = [];

    // Find links where this category is the multiplier
    foreach ($categoryLinks as $link) {
        if ($link['multiplier_category_id'] == $categoryId) {
            // Get total costs from target category
            try {
                $targetCostsQuery = $pdo->prepare("
                    SELECT SUM(co.amount * co.num_days) as total
                    FROM costs co
                    WHERE co.category_id = ?
                ");
                $targetCostsQuery->execute([$link['target_category_id']]);
                $targetTotal = floatval($targetCostsQuery->fetch()['total'] ?? 0);

                if ($targetTotal > 0) {
                    $linkedCosts[] = [
                        'target_category' => $link['target_category_name'],
                        'target_total' => $targetTotal,
                        'multiplier' => $link['multiplier'],
                        'calculated_total' => $targetTotal * $link['multiplier']
                    ];
                }
            } catch (Exception $e) {
                // Skip this link if there's an error
                continue;
            }
        }
    }

    return $linkedCosts;
}
?>

  <main class="container">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <h1 class="section-title"><i class="fas fa-money-bill-wave"></i> Cost Management</h1>
      <button id="recalculate-all-btn" class="btn btn-secondary" title="Recalculate all learner-multiplied costs">
        <i class="fas fa-calculator"></i> Recalculate All Learner Costs
      </button>
    </div>

    <div class="card">
      <div class="card-header">
        <h2><i class="fas fa-plus-circle"></i> Add New Cost</h2>
        <div class="learner-info">
          <span class="learner-count">
            <i class="fas fa-users"></i> Total Learners: <strong><?= number_format($totalLearners) ?></strong>
          </span>
        </div>
      </div>
      <form id="cost-form">
        <div class="form-group">
          <label class="form-label" for="cost-description">Description</label>
          <input type="text" id="cost-description" name="description" placeholder="Enter cost description" required>
        </div>
        <div class="form-group">
          <label class="form-label" for="cost-amount">Amount</label>
          <input type="number" id="cost-amount" name="amount" placeholder="0.00" step="0.01" required>
          <small class="form-help" id="amount-help">Enter the base amount per unit</small>
        </div>
        <div class="form-group">
          <label class="form-label" for="category-select">Category</label>
          <select name="category_id" id="category-select" required>
            <option value="">Select Category</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label" for="subcategory-select">Subcategory</label>
          <select name="subcategory_id" id="subcategory-select" required>
            <option value="">Select Category First</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label" for="rate-type">Rate Type</label>
          <select name="rate_type" id="rate-type">
            <option value="daily">Daily</option>
            <option value="monthly">Monthly</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label" for="num-days">Number of Days</label>
          <input type="number" id="num-days" name="num_days" placeholder="1" min="1" value="1" required>
        </div>

        <!-- Multiply by Learners Checkbox -->
        <div class="form-group">
          <div class="checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" id="multiply-by-learners" name="multiply_by_learners" value="1">
              <span class="checkmark"></span>
              <span class="checkbox-text">
                <strong>Multiply by all learners</strong>
                <small id="learner-count-text">Cost will be multiplied by <?= number_format($totalLearners) ?> learners</small>
                <button type="button" id="refresh-learners-btn" class="btn btn-sm btn-secondary" style="margin-left: 10px; padding: 2px 8px; font-size: 12px;" title="Refresh learner count">
                  <i class="fas fa-sync-alt"></i>
                </button>
              </span>
            </label>
          </div>
        </div>

        <!-- Cost Preview -->
        <div class="cost-preview" id="cost-preview" style="display: none;">
          <div class="preview-content">
            <h4><i class="fas fa-calculator"></i> Cost Preview</h4>
            <div class="preview-breakdown">
              <div class="preview-line">
                <span>Base Amount:</span>
                <span id="preview-base">R0.00</span>
              </div>
              <div class="preview-line">
                <span>Days:</span>
                <span id="preview-days">1</span>
              </div>
              <div class="preview-line" id="preview-learners-line" style="display: none;">
                <span>Learners:</span>
                <span id="preview-learners"><?= number_format($totalLearners) ?></span>
              </div>
              <div class="preview-line preview-total">
                <span><strong>Total Cost:</strong></span>
                <span id="preview-total"><strong>R0.00</strong></span>
              </div>
            </div>
          </div>
        </div>

        <button type="submit" class="btn btn-primary">
          <i class="fas fa-plus"></i> Add Cost
        </button>
      </form>
    </div>

    <h2 class="section-subtitle"><i class="fas fa-list"></i> Saved Costs</h2>

    <!-- Categories Dropdown Menu -->
    <div class="saved-costs-dropdown">
      <div class="dropdown-trigger">
        <button class="dropdown-btn" id="categories-dropdown-btn">
          <i class="fas fa-filter"></i> Filter by Category
          <i class="fas fa-chevron-down dropdown-arrow"></i>
        </button>
      </div>
      <div class="dropdown-menu" id="categories-dropdown-menu">
        <div class="dropdown-header">
          <i class="fas fa-bookmark"></i> Saved Costs Categories
        </div>
        <div class="dropdown-category">
          <a href="#" class="dropdown-category-link clear-filters-link" data-action="clear-filters">
            <i class="fas fa-times-circle"></i> Show All Costs
          </a>
        </div>
        <?php
        // Fetch categories and subcategories for dropdown
        try {
          $categoriesQuery = $pdo->query("
            SELECT c.id as category_id, c.name as category_name,
                   s.id as subcategory_id, s.name as subcategory_name
            FROM categories c
            LEFT JOIN subcategories s ON c.id = s.category_id
            ORDER BY c.name, s.name
          ");
          $categoriesData = $categoriesQuery->fetchAll();

          // Group by category
          $dropdownCategories = [];
          foreach ($categoriesData as $row) {
            $categoryName = $row['category_name'];
            if (!isset($dropdownCategories[$categoryName])) {
              $dropdownCategories[$categoryName] = [
                'id' => $row['category_id'],
                'subcategories' => []
              ];
            }
            if ($row['subcategory_id']) {
              $dropdownCategories[$categoryName]['subcategories'][] = [
                'id' => $row['subcategory_id'],
                'name' => $row['subcategory_name']
              ];
            }
          }
        } catch (Exception $e) {
          $dropdownCategories = [];
        }
        ?>

        <?php if (!empty($dropdownCategories)): ?>
          <?php foreach ($dropdownCategories as $categoryName => $categoryData): ?>
            <div class="dropdown-category">
              <a href="#category-<?= $categoryData['id'] ?>" class="dropdown-category-link" data-filter-category="<?= $categoryData['id'] ?>">
                <i class="fas fa-tag"></i> <?= htmlspecialchars($categoryName) ?>
              </a>
              <?php if (!empty($categoryData['subcategories'])): ?>
                <div class="dropdown-subcategories">
                  <?php foreach ($categoryData['subcategories'] as $subcategory): ?>
                    <a href="#subcategory-<?= $subcategory['id'] ?>" class="dropdown-subcategory-link" data-filter-subcategory="<?= $subcategory['id'] ?>">
                      <i class="fas fa-layer-group"></i> <?= htmlspecialchars($subcategory['name']) ?>
                    </a>
                  <?php endforeach; ?>
                </div>
              <?php endif; ?>
            </div>
          <?php endforeach; ?>
        <?php else: ?>
          <div class="dropdown-empty">
            <i class="fas fa-info-circle"></i> No categories found
          </div>
        <?php endif; ?>
      </div>
    </div>

    <?php if (empty($costs)): ?>
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">No Costs Found</h3>
        </div>
        <p class="card-subtitle">Add your first cost using the form above.</p>
      </div>
    <?php else: ?>
      <?php foreach ($groupedCosts as $categoryName => $subcategories): ?>
        <?php
        // Get category ID for navigation
        $categoryId = null;
        foreach ($subcategories as $subcategoryCosts) {
          if (!empty($subcategoryCosts)) {
            $categoryId = $subcategoryCosts[0]['category_id'];
            break;
          }
        }
        ?>
        <div class="category-section" id="category-<?= $categoryId ?>">
          <div class="category-header">
            <div class="category-header-main">
              <div class="category-title-section">
                <h3 class="category-title">
                  <i class="fas fa-folder-open category-icon"></i>
                  <span class="category-name"><?= htmlspecialchars($categoryName) ?></span>

                  <div class="category-badges">
                    <?php if (!empty($categoryInfo[$categoryName]['linked_to'])): ?>
                      <span class="category-badge badge-linked" title="This category includes linked costs">
                        <i class="fas fa-link"></i> Linked
                      </span>
                    <?php endif; ?>

                    <?php if (!empty($categoryInfo[$categoryName]['linked_from'])): ?>
                      <span class="category-badge badge-referenced" title="This category is used by other categories">
                        <i class="fas fa-share-alt"></i> Referenced
                      </span>
                    <?php endif; ?>
                  </div>
                </h3>
              </div>

              <div class="category-summary">
                <?php
                  $categoryTotal = 0;
                  $categoryCount = 0;
                  foreach ($subcategories as $subcategoryCosts) {
                    foreach ($subcategoryCosts as $cost) {
                      $categoryTotal += ($cost['amount'] * $cost['num_days']);
                      $categoryCount++;
                    }
                  }

                  // Calculate linked costs
                  $linkedCosts = calculateLinkedCategoryCosts($pdo, $categoryId, $categoryLinks);
                  $linkedTotal = 0;
                  foreach ($linkedCosts as $linked) {
                    $linkedTotal += $linked['calculated_total'];
                  }

                  $grandTotal = $categoryTotal + $linkedTotal;
                ?>
                <div class="category-stats">
                  <div class="stat-item">
                    <span class="stat-label">Items:</span>
                    <span class="stat-value"><?= $categoryCount ?></span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">Direct:</span>
                    <span class="stat-value">R<?= number_format($categoryTotal, 2) ?></span>
                  </div>
                  <?php if ($linkedTotal > 0): ?>
                    <div class="stat-item">
                      <span class="stat-label">Linked:</span>
                      <span class="stat-value linked-amount">R<?= number_format($linkedTotal, 2) ?></span>
                    </div>
                  <?php endif; ?>
                  <div class="stat-item stat-total">
                    <span class="stat-label">Total:</span>
                    <span class="stat-value total-amount">R<?= number_format($grandTotal, 2) ?></span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Category Links Information -->
            <?php if (!empty($categoryInfo[$categoryName]['linked_to']) || !empty($categoryInfo[$categoryName]['linked_from'])): ?>
              <div class="category-links-section">
                <?php if (!empty($categoryInfo[$categoryName]['linked_to'])): ?>
                  <div class="links-group">
                    <div class="links-header">
                      <i class="fas fa-arrow-right"></i>
                      <span>Includes costs from:</span>
                    </div>
                    <div class="links-items">
                      <?php foreach ($categoryInfo[$categoryName]['linked_to'] as $link): ?>
                        <div class="link-item">
                          <span class="link-category"><?= htmlspecialchars($link['target_category']) ?></span>
                          <span class="link-multiplier">× <?= number_format($link['multiplier'], 2) ?></span>
                          <a href="#category-<?= $link['target_id'] ?>" class="link-jump" title="Go to <?= htmlspecialchars($link['target_category']) ?>">
                            <i class="fas fa-external-link-alt"></i>
                          </a>
                        </div>
                      <?php endforeach; ?>
                    </div>
                  </div>
                <?php endif; ?>

                <?php if (!empty($categoryInfo[$categoryName]['linked_from'])): ?>
                  <div class="links-group">
                    <div class="links-header">
                      <i class="fas fa-arrow-left"></i>
                      <span>Used by categories:</span>
                    </div>
                    <div class="links-items">
                      <?php foreach ($categoryInfo[$categoryName]['linked_from'] as $link): ?>
                        <div class="link-item">
                          <span class="link-category"><?= htmlspecialchars($link['multiplier_category']) ?></span>
                          <span class="link-multiplier">× <?= number_format($link['multiplier'], 2) ?></span>
                          <a href="#category-<?= $link['multiplier_id'] ?>" class="link-jump" title="Go to <?= htmlspecialchars($link['multiplier_category']) ?>">
                            <i class="fas fa-external-link-alt"></i>
                          </a>
                        </div>
                      <?php endforeach; ?>
                    </div>
                  </div>
                <?php endif; ?>
              </div>
            <?php endif; ?>
          </div>

          <?php foreach ($subcategories as $subcategoryName => $subcategoryCosts): ?>
            <?php
            // Get subcategory ID for navigation
            $subcategoryId = !empty($subcategoryCosts) ? $subcategoryCosts[0]['subcategory_id'] : null;
            ?>
            <div class="subcategory-costs-section" id="subcategory-<?= $subcategoryId ?>">
              <div class="subcategory-header">
                <h3 class="category-name-in-subcategory">
                  <i class="fas fa-tag"></i> <?= htmlspecialchars($categoryName) ?>
                </h3>
                <h4 class="subcategory-title">
                  <i class="fas fa-layer-group"></i> <?= htmlspecialchars($subcategoryName) ?>
                  <span class="subcategory-count">(<?= count($subcategoryCosts) ?> costs)</span>
                </h4>
              </div>

              <div class="table-container">
                <table class="costs-table modern-table">
                  <thead>
                    <tr>
                      <th class="col-description">
                        <div class="th-content">
                          <i class="fas fa-tag"></i>
                          <span>Description</span>
                        </div>
                      </th>
                      <th class="col-amount">
                        <div class="th-content">
                          <i class="fas fa-dollar-sign"></i>
                          <span>Amount</span>
                        </div>
                      </th>
                      <th class="col-rate">
                        <div class="th-content">
                          <i class="fas fa-clock"></i>
                          <span>Rate</span>
                        </div>
                      </th>
                      <th class="col-days">
                        <div class="th-content">
                          <i class="fas fa-calendar"></i>
                          <span>Days</span>
                        </div>
                      </th>
                      <th class="col-total">
                        <div class="th-content">
                          <i class="fas fa-calculator"></i>
                          <span>Total</span>
                        </div>
                      </th>
                      <th class="col-learners">
                        <div class="th-content">
                          <i class="fas fa-users"></i>
                          <span>Learners</span>
                        </div>
                      </th>
                      <th class="col-date">
                        <div class="th-content">
                          <i class="fas fa-calendar-plus"></i>
                          <span>Added</span>
                        </div>
                      </th>
                      <th class="col-actions">
                        <div class="th-content">
                          <i class="fas fa-cog"></i>
                          <span>Actions</span>
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php foreach ($subcategoryCosts as $cost): ?>
                      <tr class="cost-row">
                        <td class="col-description">
                          <div class="cost-description">
                            <strong class="cost-title"><?= htmlspecialchars($cost['description']) ?></strong>
                          </div>
                        </td>
                        <td class="col-amount">
                          <span class="amount-value">R<?= number_format($cost['amount'], 2) ?></span>
                        </td>
                        <td class="col-rate">
                          <span class="rate-badge rate-<?= $cost['rate_type'] ?>">
                            <i class="fas fa-<?= $cost['rate_type'] === 'daily' ? 'calendar-day' : 'calendar-alt' ?>"></i>
                            <?= ucfirst($cost['rate_type']) ?>
                          </span>
                        </td>
                        <td class="col-days">
                          <span class="days-value"><?= $cost['num_days'] ?></span>
                        </td>
                        <td class="col-total">
                          <strong class="total-value">R<?= number_format($cost['amount'] * $cost['num_days'], 2) ?></strong>
                        </td>
                        <td class="col-learners">
                          <?php if ($cost['is_multiplied_by_learners']): ?>
                            <div class="learner-info-cell">
                              <div class="learner-badge-modern">
                                <i class="fas fa-users"></i>
                                <span class="learner-count"><?= number_format($cost['learner_count_used']) ?></span>
                              </div>
                              <div class="learner-details-modern">
                                <small class="per-learner-amount">
                                  R<?= number_format($cost['original_amount_per_learner'], 2) ?> per learner
                                </small>
                                <?php if ($cost['last_learner_update']): ?>
                                  <small class="update-date">
                                    Updated: <?= date('M j, Y', strtotime($cost['last_learner_update'])) ?>
                                  </small>
                                <?php endif; ?>
                              </div>
                            </div>
                          <?php else: ?>
                            <span class="no-learner-info">-</span>
                          <?php endif; ?>
                        </td>
                        <td class="col-date">
                          <span class="date-value"><?= date('M j, Y', strtotime($cost['created_at'])) ?></span>
                        </td>
                        <td class="col-actions">
                          <div class="action-buttons">
                            <button class="btn btn-sm btn-edit edit-btn" data-id="<?= $cost['id'] ?>" title="Edit Cost">
                              <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-delete delete-btn" data-id="<?= $cost['id'] ?>" title="Delete Cost">
                              <i class="fas fa-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    <?php endforeach; ?>

                    <?php
                    // Show linked costs as virtual entries (only for the first subcategory to avoid duplication)
                    if ($subcategoryName === array_key_first($subcategories)):
                      $linkedCosts = calculateLinkedCategoryCosts($pdo, $categoryId, $categoryLinks);
                      foreach ($linkedCosts as $linked):
                    ?>
                      <tr class="linked-cost-row">
                        <td>
                          <div class="linked-cost-description">
                            <i class="fas fa-link text-info"></i>
                            <strong>Linked: <?= htmlspecialchars($linked['target_category']) ?></strong>
                            <small class="linked-cost-details">
                              R<?= number_format($linked['target_total'], 2) ?> × <?= number_format($linked['multiplier'], 2) ?>
                            </small>
                          </div>
                        </td>
                        <td class="text-muted">-</td>
                        <td>
                          <span class="rate-badge rate-linked">
                            <i class="fas fa-link"></i> Linked
                          </span>
                        </td>
                        <td class="text-muted">-</td>
                        <td>
                          <strong class="linked-total">R<?= number_format($linked['calculated_total'], 2) ?></strong>
                        </td>
                        <td class="text-muted">-</td>
                        <td class="text-muted">Calculated</td>
                        <td class="text-muted">-</td>
                      </tr>
                    <?php
                      endforeach;
                    endif;
                    ?>
                  </tbody>
                  <tfoot>
                    <tr class="subcategory-total">
                      <td colspan="4"><strong>Subcategory Total:</strong></td>
                      <td>
                        <strong>
                          R<?php
                            $subcategoryTotal = 0;
                            foreach ($subcategoryCosts as $cost) {
                              $subcategoryTotal += ($cost['amount'] * $cost['num_days']);
                            }

                            // Add linked costs (only for first subcategory to avoid duplication)
                            if ($subcategoryName === array_key_first($subcategories)) {
                              $linkedCosts = calculateLinkedCategoryCosts($pdo, $categoryId, $categoryLinks);
                              foreach ($linkedCosts as $linked) {
                                $subcategoryTotal += $linked['calculated_total'];
                              }
                            }

                            echo number_format($subcategoryTotal, 2);
                          ?>
                        </strong>
                      </td>
                      <td colspan="3"></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          <?php endforeach; ?>
        </div>
      <?php endforeach; ?>
    <?php endif; ?>
  </main>

  <!-- Edit Cost Modal -->
  <div id="edit-cost-modal" class="modal" style="display:none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Edit Cost Entry</h3>
        <span class="close-modal">&times;</span>
      </div>
      <form id="edit-cost-form">
        <input type="hidden" name="id" id="edit-cost-id">
        <div class="form-group">
          <label class="form-label" for="edit-cost-description">Description</label>
          <input type="text" name="description" id="edit-cost-description" placeholder="Description" required>
        </div>
        <div class="form-group">
          <label class="form-label" for="edit-cost-amount">Amount</label>
          <input type="number" name="amount" id="edit-cost-amount" placeholder="Amount" step="0.01" required>
        </div>
        <div class="form-group">
          <label class="form-label" for="edit-cost-days">Number of Days</label>
          <input type="number" name="num_days" id="edit-cost-days" placeholder="Days" min="1" required>
        </div>
        <div class="form-group">
          <label class="form-label" for="edit-cost-rate">Rate Type</label>
          <select name="rate_type" id="edit-cost-rate">
            <option value="daily">Daily</option>
            <option value="monthly">Monthly</option>
          </select>
        </div>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-save"></i> Save Changes
        </button>
      </form>
    </div>
  </div>

  <script src="../assets/js/edit-handlers-new.js"></script>

  <script>
    // IMPORTANT: Prevent any other scripts from handling the cost form
    window.costFormHandled = false;

    // Cost calculation and preview functionality
    document.addEventListener('DOMContentLoaded', () => {

      // === DROPDOWN POPULATION ===
      const categorySelect = document.getElementById("category-select");
      const subcategorySelect = document.getElementById("subcategory-select");

      if (categorySelect) {
        console.log("Category select found, loading categories...");
        // Load categories
        fetch("../ajax/get_categories.php")
          .then(res => {
            console.log("Categories response status:", res.status);
            return res.json();
          })
          .then(data => {
            console.log("Categories data received:", data);
            categorySelect.innerHTML = '<option value="">Select Category</option>';

            if (Array.isArray(data)) {
              console.log("Processing", data.length, "categories");
              data.forEach(cat => {
                const opt = document.createElement("option");
                opt.value = cat.id;
                opt.textContent = cat.name;
                categorySelect.appendChild(opt);
                console.log("Added category:", cat.name);
              });
            } else if (data.error) {
              console.error("Server error:", data.error);
              categorySelect.innerHTML = '<option value="">Error loading categories</option>';
            } else {
              console.error("Categories data is not an array:", data);
              categorySelect.innerHTML = '<option value="">Invalid data format</option>';
            }
          })
          .catch(err => {
            console.error("Error loading categories:", err);
            categorySelect.innerHTML = '<option value="">Network error</option>';
          });

        // When category changes, load subcategories
        categorySelect.addEventListener("change", () => {
          const catId = categorySelect.value;
          if (!catId) {
            subcategorySelect.innerHTML = '<option value="">Select Category First</option>';
            return;
          }

          subcategorySelect.innerHTML = '<option>Loading…</option>';
          console.log("Loading subcategories for category:", catId);

          fetch(`../ajax/get_subcategories.php?category_id=${catId}`)
            .then(res => {
              console.log("Subcategories response status:", res.status);
              return res.json();
            })
            .then(data => {
              console.log("Subcategories data received:", data);
              subcategorySelect.innerHTML = '<option value="">Select Subcategory</option>';

              if (data.success && Array.isArray(data.subcategories)) {
                console.log("Processing", data.subcategories.length, "subcategories");
                data.subcategories.forEach(sub => {
                  const opt = document.createElement("option");
                  opt.value = sub.id;
                  opt.textContent = sub.name;
                  subcategorySelect.appendChild(opt);
                  console.log("Added subcategory:", sub.name);
                });

                if (data.subcategories.length === 0) {
                  subcategorySelect.innerHTML = '<option value="">No subcategories available</option>';
                }
              } else {
                console.error("Invalid subcategories data:", data);
                subcategorySelect.innerHTML = '<option value="">Error loading subcategories</option>';
              }
            })
            .catch(err => {
              console.error("Error loading subcategories:", err);
              subcategorySelect.innerHTML = '<option value="">Network error</option>';
            });
        });
      }

      // === FORM SUBMISSION ===
      // Prevent duplicate handlers
      if (window.costFormHandled) {
        console.log("Cost form handler already attached, skipping...");
        return;
      }

      const costForm = document.getElementById("cost-form");
      if (costForm) {
        // Mark as handled to prevent other scripts from adding handlers
        window.costFormHandled = true;

        // Remove any existing event listeners by cloning
        const newCostForm = costForm.cloneNode(true);
        costForm.parentNode.replaceChild(newCostForm, costForm);

        // Add single consolidated event listener with submission tracking
        let isSubmitting = false;
        newCostForm.addEventListener("submit", async e => {
          e.preventDefault();

          // Prevent multiple simultaneous submissions
          if (isSubmitting) {
            console.log("Submission already in progress, ignoring...");
            return;
          }
          isSubmitting = true;

          // Debug: Log form submission to detect duplicates
          console.log("Cost form submitted at:", new Date().toISOString());

          // Disable submit button to prevent double-clicks
          const submitButton = newCostForm.querySelector('button[type="submit"]');
          const originalButtonText = submitButton.innerHTML;
          submitButton.disabled = true;
          submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';

          // Handle learner multiplier calculation BEFORE submission
          const multiplyCheckbox = document.getElementById('multiply-by-learners');
          const amountInput = document.getElementById('cost-amount');
          const daysInput = document.getElementById('num-days');

          if (multiplyCheckbox && multiplyCheckbox.checked) {
            // Ensure we have the latest total learners count
            await fetchTotalLearners();

            // Calculate total amount including learner multiplier
            const originalAmount = parseFloat(amountInput.value) || 0;
            const days = parseInt(daysInput.value) || 1;
            const totalAmount = originalAmount * days * totalLearners;

            // Update form data for submission
            amountInput.value = totalAmount;
            daysInput.value = 1; // Set to 1 since we've calculated the total

            // Add hidden fields for reference
            const multiplierField = document.createElement('input');
            multiplierField.type = 'hidden';
            multiplierField.name = 'learner_multiplier';
            multiplierField.value = totalLearners;
            newCostForm.appendChild(multiplierField);

            const originalAmountField = document.createElement('input');
            originalAmountField.type = 'hidden';
            originalAmountField.name = 'original_amount';
            originalAmountField.value = originalAmount;
            newCostForm.appendChild(originalAmountField);
          }

          try {
            const fd = new FormData(newCostForm);

            // Add unique submission token for duplicate prevention
            const submissionToken = Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            fd.append('submission_token', submissionToken);
            console.log("Submission token:", submissionToken);

            const res = await fetch("../ajax/save_cost.php", { method: "POST", body: fd });
            const data = await res.json();

            if (data.success) {
              alert(data.message);
              newCostForm.reset();
              // Reset dropdowns
              categorySelect.value = "";
              subcategorySelect.innerHTML = '<option value="">Select Category First</option>';
              // Refresh the page to show the updated table
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            } else {
              alert(data.message || "An error occurred");
              // Restore button state on error
              submitButton.disabled = false;
              submitButton.innerHTML = originalButtonText;
            }
          } catch (error) {
            console.error("Error saving cost:", error);
            alert("Failed to save cost. Please try again.");
            // Restore button state on error
            submitButton.disabled = false;
            submitButton.innerHTML = originalButtonText;
          } finally {
            // Always reset submission flag
            isSubmitting = false;
          }
        });
      }
      const amountInput = document.getElementById('cost-amount');
      const daysInput = document.getElementById('num-days');
      const multiplyCheckbox = document.getElementById('multiply-by-learners');
      const costPreview = document.getElementById('cost-preview');
      const previewBase = document.getElementById('preview-base');
      const previewDays = document.getElementById('preview-days');
      const previewLearners = document.getElementById('preview-learners');
      const previewLearnersLine = document.getElementById('preview-learners-line');
      const previewTotal = document.getElementById('preview-total');
      const amountHelp = document.getElementById('amount-help');

      let totalLearners = <?= $totalLearners ?>; // Make it mutable

      // Function to fetch current total learners from database
      async function fetchTotalLearners() {
        try {
          const response = await fetch('../ajax/get_total_learners.php');
          const data = await response.json();
          if (data.success) {
            totalLearners = data.total_learners;
            // Update the preview learners display
            previewLearners.textContent = totalLearners.toLocaleString();
            // Update the checkbox text
            const learnerCountText = document.getElementById('learner-count-text');
            if (learnerCountText) {
              learnerCountText.textContent = `Cost will be multiplied by ${totalLearners.toLocaleString()} learners`;
            }
            // Update the preview and help text
            updateCostPreview();
            updateAmountHelp();
            return totalLearners;
          } else {
            console.error('Failed to fetch total learners:', data.message);
            return totalLearners; // Return current value if fetch fails
          }
        } catch (error) {
          console.error('Error fetching total learners:', error);
          return totalLearners; // Return current value if fetch fails
        }
      }

      function updateCostPreview() {
        const amount = parseFloat(amountInput.value) || 0;
        const days = parseInt(daysInput.value) || 1;
        const multiplyByLearners = multiplyCheckbox.checked;

        if (amount > 0) {
          costPreview.style.display = 'block';

          // Update preview values
          previewBase.textContent = 'R' + amount.toLocaleString('en-ZA', { minimumFractionDigits: 2 });
          previewDays.textContent = days;

          // Calculate total
          let total = amount * days;
          if (multiplyByLearners) {
            total *= totalLearners;
            previewLearnersLine.style.display = 'flex';
            previewLearners.textContent = totalLearners.toLocaleString();
          } else {
            previewLearnersLine.style.display = 'none';
          }

          previewTotal.textContent = 'R' + total.toLocaleString('en-ZA', { minimumFractionDigits: 2 });
        } else {
          costPreview.style.display = 'none';
        }
      }

      function updateAmountHelp() {
        if (multiplyCheckbox.checked) {
          amountHelp.textContent = `Enter amount per learner (will be multiplied by ${totalLearners.toLocaleString()} learners)`;
          amountHelp.style.color = 'var(--primary)';
          amountHelp.style.fontWeight = '500';
        } else {
          amountHelp.textContent = 'Enter the base amount per unit';
          amountHelp.style.color = 'var(--text-secondary)';
          amountHelp.style.fontWeight = 'normal';
        }
      }

      // Event listeners
      amountInput.addEventListener('input', updateCostPreview);
      daysInput.addEventListener('input', updateCostPreview);
      multiplyCheckbox.addEventListener('change', async () => {
        if (multiplyCheckbox.checked) {
          // Fetch the latest total learners when checkbox is checked
          await fetchTotalLearners();
        }
        updateCostPreview();
        updateAmountHelp();
      });

      // Refresh learners button
      const refreshLearnersBtn = document.getElementById('refresh-learners-btn');
      if (refreshLearnersBtn) {
        refreshLearnersBtn.addEventListener('click', async () => {
          // Show loading state
          const originalIcon = refreshLearnersBtn.innerHTML;
          refreshLearnersBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
          refreshLearnersBtn.disabled = true;

          try {
            await fetchTotalLearners();
            // Show success feedback
            refreshLearnersBtn.innerHTML = '<i class="fas fa-check"></i>';
            setTimeout(() => {
              refreshLearnersBtn.innerHTML = originalIcon;
              refreshLearnersBtn.disabled = false;
            }, 1000);
          } catch (error) {
            // Show error feedback
            refreshLearnersBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
            setTimeout(() => {
              refreshLearnersBtn.innerHTML = originalIcon;
              refreshLearnersBtn.disabled = false;
            }, 2000);
          }
        });
      }

      // Fetch total learners when page loads to ensure we have the latest count
      fetchTotalLearners();

      // Auto-refresh learner count when window gains focus (user might have updated revenue in another tab)
      window.addEventListener('focus', () => {
        if (multiplyCheckbox.checked) {
          fetchTotalLearners();
        }
      });

      // Listen for revenue data changes from other tabs/pages
      let lastRevenueChangeTime = localStorage.getItem('revenueDataChanged');

      // Check for revenue data changes periodically
      setInterval(() => {
        const currentRevenueChangeTime = localStorage.getItem('revenueDataChanged');
        if (currentRevenueChangeTime && currentRevenueChangeTime !== lastRevenueChangeTime) {
          lastRevenueChangeTime = currentRevenueChangeTime;
          // Revenue data has changed, refresh learner count
          fetchTotalLearners();
          console.log('Revenue data changed, refreshing learner count...');
        }
      }, 2000); // Check every 2 seconds

      // Also listen for storage events (when localStorage changes in other tabs)
      window.addEventListener('storage', (e) => {
        if (e.key === 'revenueDataChanged') {
          fetchTotalLearners();
          console.log('Revenue data changed in another tab, refreshing learner count...');
        }
      });

      // Recalculate all learner costs button
      const recalculateAllBtn = document.getElementById('recalculate-all-btn');
      if (recalculateAllBtn) {
        recalculateAllBtn.addEventListener('click', async () => {
          if (!confirm('This will recalculate all costs that are multiplied by learners based on the current learner count. Continue?')) {
            return;
          }

          // Show loading state
          const originalText = recalculateAllBtn.innerHTML;
          recalculateAllBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Recalculating...';
          recalculateAllBtn.disabled = true;

          try {
            const response = await fetch('../ajax/recalculate_learner_costs.php', {
              method: 'POST'
            });
            const data = await response.json();

            if (data.success) {
              if (data.updated_costs > 0) {
                alert(`✅ Successfully recalculated ${data.updated_costs} cost(s) for ${data.current_learner_count} learners.`);
                // Refresh the page to show updated costs
                setTimeout(() => {
                  window.location.reload();
                }, 1000);
              } else {
                alert('ℹ️ No learner-multiplied costs found to recalculate.');
              }
            } else {
              alert('❌ Error: ' + data.message);
            }
          } catch (error) {
            console.error('Error recalculating costs:', error);
            alert('❌ Failed to recalculate costs. Please try again.');
          } finally {
            // Restore button state
            recalculateAllBtn.innerHTML = originalText;
            recalculateAllBtn.disabled = false;
          }
        });
      }

      // Initialize
      updateAmountHelp();

      // Note: Learner multiplier calculation is now handled in the main form submission handler above
    });
  </script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

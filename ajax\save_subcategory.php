<?php
// /ajax/save_subcategory.php
session_start();
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../includes/auth.php';

// Ensure only admins can add subcategories
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
  header('Content-Type: application/json');
  echo json_encode(['success' => false, 'message' => 'Unauthorized access.']);
  exit;
}

// Set content type to JSON
header('Content-Type: application/json');

// Debug information
$debug = [];
$debug['post_data'] = $_POST;
$debug['session'] = $_SESSION;

try {
  // Get form data
  $category_id = intval($_POST['category_id'] ?? 0);
  $name = trim($_POST['name'] ?? '');
  $description = trim($_POST['description'] ?? '');

  $debug['category_id'] = $category_id;
  $debug['name'] = $name;
  $debug['description'] = $description;

  // Validate input
  if (empty($name)) {
    throw new Exception('Subcategory name is required.');
  }

  if ($category_id <= 0) {
    throw new Exception('Valid category ID is required.');
  }

  // Check if category exists
  $check = $pdo->prepare("SELECT id FROM categories WHERE id = ?");
  $check->execute([$category_id]);
  if (!$check->fetch()) {
    throw new Exception('The selected category does not exist.');
  }

  // Check if subcategory already exists in this category
  $check = $pdo->prepare("SELECT id FROM subcategories WHERE category_id = ? AND name = ?");
  $check->execute([$category_id, $name]);
  if ($check->fetch()) {
    throw new Exception('A subcategory with this name already exists in this category.');
  }

  // Insert the new subcategory
  $stmt = $pdo->prepare("INSERT INTO subcategories (category_id, name, description) VALUES (?, ?, ?)");
  $result = $stmt->execute([$category_id, $name, $description]);

  if (!$result) {
    throw new Exception('Failed to add subcategory to database.');
  }

  $subcategoryId = $pdo->lastInsertId();
  $debug['subcategory_id'] = $subcategoryId;

  // Return success response
  echo json_encode([
    'success' => true,
    'message' => 'Subcategory added successfully.',
    'subcategory' => [
      'id' => $subcategoryId,
      'category_id' => $category_id,
      'name' => $name,
      'description' => $description
    ],
    'debug' => $debug
  ]);

} catch (Exception $e) {
  // Return error response
  echo json_encode([
    'success' => false,
    'message' => $e->getMessage(),
    'debug' => $debug
  ]);
}
?>

<?php
/**
 * Database Migration: Add learner multiplier fields to costs table
 * This script adds fields to track costs that are multiplied by learner count
 */

require_once __DIR__ . '/config/db.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Database Migration - Add Learner Multiplier Fields</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; padding: 10px; background: #e8f5e8; border-radius: 5px; margin: 10px 0; }
        .error { color: red; padding: 10px; background: #ffe8e8; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; padding: 10px; background: #e8f0ff; border-radius: 5px; margin: 10px 0; }
        .warning { color: orange; padding: 10px; background: #fff8e8; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>";

echo "<h1>🔧 Database Migration: Add Learner Multiplier Fields</h1>";

try {
    // Check if the new columns already exist
    $checkColumns = $pdo->query("SHOW COLUMNS FROM costs LIKE 'is_multiplied_by_learners'");
    $columnExists = $checkColumns->fetch();
    
    if ($columnExists) {
        echo "<div class='warning'>⚠️ Learner multiplier fields already exist in the costs table.</div>";
    } else {
        echo "<div class='info'>📝 Adding learner multiplier fields to costs table...</div>";
        
        // Add the new columns
        $alterQueries = [
            "ALTER TABLE costs ADD COLUMN is_multiplied_by_learners BOOLEAN DEFAULT FALSE AFTER num_days",
            "ALTER TABLE costs ADD COLUMN original_amount_per_learner DECIMAL(10,2) NULL AFTER is_multiplied_by_learners",
            "ALTER TABLE costs ADD COLUMN learner_count_used INT NULL AFTER original_amount_per_learner",
            "ALTER TABLE costs ADD COLUMN last_learner_update TIMESTAMP NULL AFTER learner_count_used"
        ];
        
        foreach ($alterQueries as $query) {
            $pdo->exec($query);
        }
        
        echo "<div class='success'>✅ Successfully added learner multiplier fields to costs table!</div>";
        
        // Show the new table structure
        echo "<div class='info'>📋 New table structure:</div>";
        echo "<ul>";
        echo "<li><strong>is_multiplied_by_learners</strong> - Boolean flag to track if cost was multiplied by learners</li>";
        echo "<li><strong>original_amount_per_learner</strong> - Original amount per learner before multiplication</li>";
        echo "<li><strong>learner_count_used</strong> - Number of learners used in the calculation</li>";
        echo "<li><strong>last_learner_update</strong> - Timestamp of last learner count update</li>";
        echo "</ul>";
    }
    
    // Show current table structure
    echo "<div class='info'>📊 Current costs table structure:</div>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $columns = $pdo->query("SHOW COLUMNS FROM costs");
    while ($column = $columns->fetch()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div class='success'>🎉 Migration completed successfully!</div>";
    echo "<div class='info'>💡 You can now use the learner multiplier functionality with automatic recalculation when learner counts change.</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error during migration: " . htmlspecialchars($e->getMessage()) . "</div>";
    echo "<div class='info'>🔍 Please check your database connection and permissions.</div>";
}

echo "</body></html>";
?>

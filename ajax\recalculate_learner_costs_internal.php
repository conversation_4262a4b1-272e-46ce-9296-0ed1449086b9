<?php
/**
 * Internal function to recalculate learner-multiplied costs
 * This function can be included and called from other PHP files
 */

function recalculateLearnerCosts($pdo) {
    try {
        // Get the current total learners
        $stmt = $pdo->query("SELECT SUM(total_students) as total FROM revenue");
        $result = $stmt->fetch();
        $currentTotalLearners = intval($result['total'] ?? 0);
        
        if ($currentTotalLearners <= 0) {
            return [
                'success' => false,
                'message' => 'No learners found in revenue data. Cannot recalculate costs.',
                'updated_costs' => 0,
                'current_learner_count' => 0
            ];
        }
        
        // Find all costs that are multiplied by learners
        $findCostsStmt = $pdo->prepare("
            SELECT id, description, original_amount_per_learner, learner_count_used, num_days
            FROM costs 
            WHERE is_multiplied_by_learners = TRUE 
            AND original_amount_per_learner IS NOT NULL 
            AND learner_count_used IS NOT NULL
        ");
        $findCostsStmt->execute();
        $learnerCosts = $findCostsStmt->fetchAll();
        
        $updatedCount = 0;
        $updateDetails = [];
        
        foreach ($learnerCosts as $cost) {
            $oldAmount = $cost['original_amount_per_learner'] * $cost['num_days'] * $cost['learner_count_used'];
            $newAmount = $cost['original_amount_per_learner'] * $cost['num_days'] * $currentTotalLearners;
            
            // Only update if the amount actually changes
            if ($oldAmount != $newAmount) {
                $updateStmt = $pdo->prepare("
                    UPDATE costs 
                    SET amount = ?, 
                        learner_count_used = ?, 
                        last_learner_update = NOW() 
                    WHERE id = ?
                ");
                $updateStmt->execute([$newAmount, $currentTotalLearners, $cost['id']]);
                
                $updatedCount++;
                $updateDetails[] = [
                    'id' => $cost['id'],
                    'description' => $cost['description'],
                    'old_amount' => $oldAmount,
                    'new_amount' => $newAmount,
                    'old_learner_count' => $cost['learner_count_used'],
                    'new_learner_count' => $currentTotalLearners
                ];
            }
        }
        
        return [
            'success' => true,
            'message' => "Successfully recalculated {$updatedCount} cost(s) for {$currentTotalLearners} learners.",
            'updated_costs' => $updatedCount,
            'current_learner_count' => $currentTotalLearners,
            'total_learner_costs_found' => count($learnerCosts),
            'update_details' => $updateDetails
        ];
        
    } catch (Exception $e) {
        error_log("Error in recalculateLearnerCosts: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Error recalculating learner costs: ' . $e->getMessage(),
            'updated_costs' => 0,
            'current_learner_count' => 0
        ];
    }
}
?>

<?php
// /ajax/get_total_learners.php
require_once __DIR__ . '/../config/db.php';
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        // Get total number of learners from revenue data
        $stmt = $pdo->query("SELECT SUM(total_students) as total FROM revenue");
        $result = $stmt->fetch();
        $totalLearners = intval($result['total'] ?? 0);
        
        echo json_encode([
            'success' => true,
            'total_learners' => $totalLearners
        ]);
    } catch (Exception $e) {
        error_log("Error fetching total learners: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => 'Error fetching total learners: ' . $e->getMessage(),
            'total_learners' => 0
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method',
        'total_learners' => 0
    ]);
}
?>

/**
 * Edit and Delete handlers for dashboard
 * Handles edit and delete operations with toast notifications
 */

// Test function to verify script is loaded
window.testEditHandlers = function() {
  console.log('Edit handlers test function called');
  const editButtons = document.querySelectorAll('.edit-btn');
  console.log(`Test found ${editButtons.length} edit buttons`);
  if (editButtons.length > 0) {
    console.log('First edit button:', editButtons[0]);
    console.log('First edit button data-id:', editButtons[0].dataset.id);
  }
};

// Test function to show modal manually
window.testModal = function() {
  console.log('Testing modal display...');
  const modal = document.getElementById('edit-cost-modal');
  if (modal) {
    console.log('Modal found, showing it');
    modal.style.display = 'flex';
    modal.classList.add('active');
    return 'Modal should now be visible';
  } else {
    console.error('Modal not found!');
    return 'Modal not found';
  }
};

// Test function to hide modal
window.hideModal = function() {
  const modal = document.getElementById('edit-cost-modal');
  if (modal) {
    modal.style.display = 'none';
    modal.classList.remove('active');
    return 'Modal hidden';
  }
  return 'Modal not found';
};

document.addEventListener('DOMContentLoaded', () => {
  console.log('Edit handlers initialized');

  // Debug: Check if elements exist
  const editButtons = document.querySelectorAll('.edit-btn');
  const deleteButtons = document.querySelectorAll('.delete-btn');
  console.log(`Found ${editButtons.length} edit buttons and ${deleteButtons.length} delete buttons`);

  // Test if showToast is available
  if (typeof window.showToast === 'function') {
    console.log('showToast function is available');
  } else {
    console.error('showToast function is NOT available');
  }

  // Modal handling
  const modals = document.querySelectorAll('.modal');
  const closeButtons = document.querySelectorAll('.close-modal');
  console.log(`Found ${modals.length} modals and ${closeButtons.length} close buttons`);

  // Close modal when clicking the close button
  closeButtons.forEach(btn => {
    btn.addEventListener('click', () => {
      modals.forEach(modal => {
        modal.style.display = 'none';
        modal.classList.remove('active');
      });
    });
  });

  // Close modal when clicking outside the modal content
  window.addEventListener('click', (e) => {
    modals.forEach(modal => {
      if (e.target === modal) {
        modal.style.display = 'none';
        modal.classList.remove('active');
      }
    });
  });

  // === COST EDIT & DELETE ===
  // Edit cost button click - Use event delegation for better reliability
  document.addEventListener('click', async (e) => {
    if (e.target.closest('.edit-btn')) {
      e.preventDefault();
      const btn = e.target.closest('.edit-btn');
      const costId = btn.dataset.id;
      console.log(`Edit button clicked for cost ID: ${costId}`);

      // Show modal immediately for testing
      const modal = document.getElementById('edit-cost-modal');
      if (modal) {
        console.log('Modal found, showing it');
        modal.style.display = 'flex';
        modal.classList.add('active');

        // Try to fetch and populate data
        try {
          console.log('Fetching cost data...');
          const response = await fetch(`../ajax/view_cost.php?id=${costId}`);
          const text = await response.text();
          console.log('Raw response:', text);

          const data = JSON.parse(text);
          console.log('Parsed data:', data);

          if (data.success) {
            // Populate form
            const idField = document.getElementById('edit-cost-id');
            const descField = document.getElementById('edit-cost-description');
            const amountField = document.getElementById('edit-cost-amount');
            const daysField = document.getElementById('edit-cost-days');
            const rateField = document.getElementById('edit-cost-rate');

            if (idField) idField.value = data.cost.id;
            if (descField) descField.value = data.cost.description;
            if (amountField) amountField.value = data.cost.amount;
            if (daysField) daysField.value = data.cost.num_days;
            if (rateField) rateField.value = data.cost.rate_type;

            console.log('Form populated successfully');
          } else {
            console.error('Failed to load cost data:', data.message);
            if (window.showToast) {
              window.showToast(data.message || "Failed to load cost data", "error");
            } else {
              alert(data.message || "Failed to load cost data");
            }
          }
        } catch (error) {
          console.error("Error fetching cost data:", error);
          if (window.showToast) {
            window.showToast("Failed to load cost data: " + error.message, "error");
          } else {
            alert("Failed to load cost data: " + error.message);
          }
        }
      } else {
        console.error('Modal not found!');
        alert('Edit modal not found');
      }
    }
  });

  // Edit cost form submit
  const editCostForm = document.getElementById('edit-cost-form');
  if (editCostForm) {
    editCostForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      console.log('Edit cost form submitted');

      try {
        // Log form data for debugging
        const formData = new FormData(editCostForm);
        console.log("Submitting edit form with data:");
        for (let [key, value] of formData.entries()) {
          console.log(`${key}: ${value}`);
        }

        // Send the form data to the server
        const response = await fetch('../ajax/update_cost.php', {
          method: 'POST',
          body: formData
        });

        // Parse the response
        const text = await response.text();
        console.log("Raw response:", text);

        let data;
        try {
          data = JSON.parse(text);
        } catch (e) {
          console.error("Failed to parse JSON response:", e);
          window.showToast("Server returned invalid response", "error");
          return;
        }

        // Handle the response
        if (data.success) {
          window.showToast(data.message || "Cost updated successfully", "success");
          const modal = document.getElementById('edit-cost-modal');
          modal.style.display = 'none';
          modal.classList.remove('active');

          // Refresh the page after a short delay
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } else {
          window.showToast(data.message || "Failed to update cost", "error");
        }
      } catch (error) {
        console.error("Error updating cost:", error);
        window.showToast("Failed to update cost: " + error.message, "error");
      }
    });
  }

  // Delete cost button click - Use event delegation
  document.addEventListener('click', async (e) => {
    if (e.target.closest('.delete-btn')) {
      e.preventDefault();
      const btn = e.target.closest('.delete-btn');
      const costId = btn.dataset.id;
      console.log(`Delete button clicked for cost ID: ${costId}`);

      if (confirm("Are you sure you want to delete this cost entry?")) {
        try {
          const response = await fetch('../ajax/delete_cost.php', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id: costId })
          });
          const data = await response.json();

          if (data.success) {
            if (window.showToast) {
              window.showToast(data.message || "Cost deleted successfully", "success");
            } else {
              alert(data.message || "Cost deleted successfully");
            }

            // Refresh the page after a short delay
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            if (window.showToast) {
              window.showToast(data.message || "Failed to delete cost", "error");
            } else {
              alert(data.message || "Failed to delete cost");
            }
          }
        } catch (error) {
          console.error("Error deleting cost:", error);
          if (window.showToast) {
            window.showToast("Failed to delete cost", "error");
          } else {
            alert("Failed to delete cost");
          }
        }
      }
    }
  });

  // === REVENUE EDIT & DELETE ===
  // Edit revenue button click
  document.addEventListener('click', async (e) => {
    if (e.target.closest('.edit-revenue')) {
      e.preventDefault();
      const btn = e.target.closest('.edit-revenue');
      const revenueId = btn.dataset.id;
      console.log(`Edit revenue button clicked for ID: ${revenueId}`);

      // Show modal
      const modal = document.getElementById('edit-revenue-modal');
      if (modal) {
        modal.style.display = 'flex';
        modal.classList.add('active');

        try {
          const response = await fetch(`../ajax/view_revenue.php?id=${revenueId}`);
          const data = await response.json();

          if (data.success) {
            document.getElementById('edit-revenue-id').value = data.revenue.id;
            document.getElementById('edit-revenue-month').value = data.revenue.month_year;
            document.getElementById('edit-revenue-type').value = data.revenue.student_type;
            document.getElementById('edit-revenue-rate').value = data.revenue.revenue_per_student;
            document.getElementById('edit-revenue-total').value = data.revenue.total_students;
          } else {
            if (window.showToast) {
              window.showToast(data.message || "Failed to load revenue data", "error");
            } else {
              alert(data.message || "Failed to load revenue data");
            }
          }
        } catch (error) {
          console.error("Error fetching revenue data:", error);
          if (window.showToast) {
            window.showToast("Failed to load revenue data", "error");
          } else {
            alert("Failed to load revenue data");
          }
        }
      }
    }
  });

  // Delete revenue button click
  document.addEventListener('click', async (e) => {
    if (e.target.closest('.delete-revenue')) {
      e.preventDefault();
      const btn = e.target.closest('.delete-revenue');
      const revenueId = btn.dataset.id;
      console.log(`Delete revenue button clicked for ID: ${revenueId}`);

      if (confirm("Are you sure you want to delete this revenue entry?")) {
        try {
          const response = await fetch('../ajax/delete_revenue.php', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id: revenueId })
          });
          const data = await response.json();

          if (data.success) {
            if (window.showToast) {
              window.showToast(data.message || "Revenue deleted successfully", "success", 5000);

              // Show additional info if costs were recalculated
              if (data.recalculation_result && data.recalculation_result.updated_costs > 0) {
                setTimeout(() => {
                  window.showToast(`🔄 ${data.recalculation_result.updated_costs} cost(s) automatically recalculated for ${data.recalculation_result.current_learner_count} learners`, "info", 4000);
                }, 1000);
              }
            } else {
              alert(data.message || "Revenue deleted successfully");
            }

            // Notify other tabs/pages that revenue data has changed
            localStorage.setItem('revenueDataChanged', Date.now().toString());

            setTimeout(() => {
              window.location.reload();
            }, 2000);
          } else {
            if (window.showToast) {
              window.showToast(data.message || "Failed to delete revenue", "error");
            } else {
              alert(data.message || "Failed to delete revenue");
            }
          }
        } catch (error) {
          console.error("Error deleting revenue:", error);
          if (window.showToast) {
            window.showToast("Failed to delete revenue", "error");
          } else {
            alert("Failed to delete revenue");
          }
        }
      }
    }
  });

  // Edit revenue form submit
  const editRevenueForm = document.getElementById('edit-revenue-form');
  if (editRevenueForm) {
    editRevenueForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      console.log('Edit revenue form submitted');

      try {
        const formData = new FormData(editRevenueForm);
        const response = await fetch('../ajax/update_revenue.php', {
          method: 'POST',
          body: formData
        });
        const data = await response.json();

        if (data.success) {
          if (window.showToast) {
            window.showToast(data.message || "Revenue updated successfully", "success", 5000);

            // Show additional info if costs were recalculated
            if (data.recalculation_result && data.recalculation_result.updated_costs > 0) {
              setTimeout(() => {
                window.showToast(`🔄 ${data.recalculation_result.updated_costs} cost(s) automatically recalculated for ${data.recalculation_result.current_learner_count} learners`, "info", 4000);
              }, 1000);
            }
          } else {
            alert(data.message || "Revenue updated successfully");
          }

          // Notify other tabs/pages that revenue data has changed
          localStorage.setItem('revenueDataChanged', Date.now().toString());

          const modal = document.getElementById('edit-revenue-modal');
          modal.style.display = 'none';
          modal.classList.remove('active');
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } else {
          if (window.showToast) {
            window.showToast(data.message || "Failed to update revenue", "error");
          } else {
            alert(data.message || "Failed to update revenue");
          }
        }
      } catch (error) {
        console.error("Error updating revenue:", error);
        if (window.showToast) {
          window.showToast("Failed to update revenue", "error");
        } else {
          alert("Failed to update revenue");
        }
      }
    });
  }
});

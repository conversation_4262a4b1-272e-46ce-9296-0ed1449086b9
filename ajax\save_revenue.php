<?php
// /ajax/save_revenue.php
require_once __DIR__ . '/../config/db.php';
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $month_year = $_POST['month_year'] ?? '';
    $student_type = $_POST['student_type'] ?? '';
    $revenue_per_student = floatval($_POST['revenue_per_student'] ?? 0);
    $total_students = intval($_POST['total_students'] ?? 0);

    if ($month_year && $student_type && $revenue_per_student > 0 && $total_students > 0) {
        try {
            // Begin transaction
            $pdo->beginTransaction();

            // Insert the revenue record
            $stmt = $pdo->prepare("INSERT INTO revenue (month_year, student_type, revenue_per_student, total_students) VALUES (?, ?, ?, ?)");
            $stmt->execute([$month_year, $student_type, $revenue_per_student, $total_students]);

            // Recalculate learner-multiplied costs
            include_once __DIR__ . '/recalculate_learner_costs_internal.php';
            $recalcResult = recalculateLearnerCosts($pdo);

            // Commit transaction
            $pdo->commit();

            $message = 'Revenue recorded successfully.';
            if ($recalcResult['updated_costs'] > 0) {
                $message .= " Automatically updated {$recalcResult['updated_costs']} cost(s) to reflect new learner count.";
            }

            echo json_encode([
                'success' => true,
                'message' => $message,
                'recalculation_result' => $recalcResult
            ]);
        } catch (Exception $e) {
            // Rollback on error
            if ($pdo->inTransaction()) {
                $pdo->rollback();
            }
            echo json_encode(['success' => false, 'message' => 'Error saving revenue: ' . $e->getMessage()]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid revenue data.']);
    }
    exit();
}
?>
<?php
// /ajax/delete_category_link.php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../includes/auth.php';

// Ensure user is authenticated as admin
authenticate_admin();

header('Content-Type: application/json');

try {
  // Get link ID
  $link_id = intval($_POST['link_id'] ?? 0);

  if ($link_id <= 0) {
    throw new Exception('Valid link ID is required.');
  }

  // Check if link exists
  $check = $pdo->prepare("SELECT id FROM category_links WHERE id = ?");
  $check->execute([$link_id]);
  if (!$check->fetch()) {
    throw new Exception('Category link not found.');
  }

  // Delete the category link
  $stmt = $pdo->prepare("DELETE FROM category_links WHERE id = ?");
  $result = $stmt->execute([$link_id]);

  if (!$result) {
    throw new Exception('Failed to delete category link.');
  }

  // Return success response
  echo json_encode([
    'success' => true,
    'message' => 'Category link deleted successfully.'
  ]);

} catch (Exception $e) {
  // Return error response
  echo json_encode([
    'success' => false,
    'message' => $e->getMessage()
  ]);
}
?>

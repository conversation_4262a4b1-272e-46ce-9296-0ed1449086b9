# Dynamic Learner Cost Multiplier Implementation

## Overview
This implementation provides automatic recalculation of costs when the number of learners changes in the revenue data. When learners are added or removed, all costs that were previously multiplied by learners will be automatically updated to reflect the new learner count.

## Key Features

### 1. **Database Schema Updates**
- Added new fields to the `costs` table:
  - `is_multiplied_by_learners` (BOOLEAN) - Tracks if cost was multiplied by learners
  - `original_amount_per_learner` (DECIMAL) - Original amount per learner before multiplication
  - `learner_count_used` (INT) - Number of learners used in the calculation
  - `last_learner_update` (TIMESTAMP) - When the cost was last recalculated

### 2. **Automatic Recalculation**
- **When revenue is added**: All learner-multiplied costs are recalculated
- **When revenue is updated**: All learner-multiplied costs are recalculated
- **When revenue is deleted**: All learner-multiplied costs are recalculated
- **Manual recalculation**: Admin button to manually trigger recalculation

### 3. **Real-time Updates**
- Cross-tab communication using localStorage
- Automatic refresh when learner count changes
- Visual feedback showing recalculation results

## Files Modified/Created

### New Files:
1. `add_learner_multiplier_fields.php` - Database migration script
2. `ajax/recalculate_learner_costs.php` - Manual recalculation endpoint
3. `ajax/recalculate_learner_costs_internal.php` - Internal recalculation function
4. `ajax/get_total_learners.php` - Fetch current learner count

### Modified Files:
1. `admin/costs.php` - Updated UI and JavaScript for dynamic learner handling
2. `ajax/save_cost.php` - Store learner multiplier information
3. `ajax/save_revenue.php` - Trigger recalculation on revenue add
4. `ajax/update_revenue.php` - Trigger recalculation on revenue update
5. `ajax/delete_revenue.php` - Trigger recalculation on revenue delete
6. `assets/js/dashboard.js` - Enhanced feedback for recalculation
7. `assets/js/edit-handlers-new.js` - Enhanced feedback for recalculation
8. `assets/css/style.css` - Styles for learner information display

## How It Works

### Cost Creation with Learner Multiplier:
1. User enters base amount per learner (e.g., R100)
2. User checks "Multiply by all learners" checkbox
3. System fetches current total learners (e.g., 12)
4. Final amount is calculated: R100 × 12 = R1,200
5. Database stores:
   - `amount`: R1,200 (final calculated amount)
   - `is_multiplied_by_learners`: true
   - `original_amount_per_learner`: R100
   - `learner_count_used`: 12

### Automatic Recalculation:
1. User updates revenue data (changes learner count to 15)
2. System automatically finds all costs where `is_multiplied_by_learners = true`
3. For each cost, recalculates: `original_amount_per_learner × num_days × new_learner_count`
4. Updates the cost amount and `learner_count_used` field
5. Shows user feedback: "3 cost(s) automatically recalculated for 15 learners"

## User Interface Enhancements

### Costs Table:
- New "Learner Info" column shows:
  - Learner badge with count (e.g., "👥 12 learners")
  - Original amount per learner
  - Last update timestamp

### Cost Form:
- Dynamic learner count display
- Refresh button to manually update learner count
- Real-time preview showing calculations

### Admin Controls:
- "Recalculate All Learner Costs" button
- Visual feedback during recalculation
- Success/error notifications

## Installation Instructions

### 1. Run Database Migration:
```
http://your-domain/add_learner_multiplier_fields.php
```

### 2. Verify Installation:
- Check that new columns exist in costs table
- Test creating a cost with learner multiplier
- Test updating revenue data and verify automatic recalculation

## Example Scenarios

### Scenario 1: Adding Learners
- Initial: 10 learners, cost = R100 × 10 = R1,000
- Update: Add 2 learners (total = 12)
- Result: Cost automatically updates to R100 × 12 = R1,200

### Scenario 2: Removing Learners
- Initial: 15 learners, cost = R50 × 15 = R750
- Update: Remove 3 learners (total = 12)
- Result: Cost automatically updates to R50 × 12 = R600

### Scenario 3: Multiple Costs
- 5 costs multiplied by learners
- Change learner count from 20 to 25
- All 5 costs automatically recalculated
- User sees: "5 cost(s) automatically recalculated for 25 learners"

## Technical Details

### Database Transactions:
- All revenue operations use transactions
- Recalculation is atomic (all or nothing)
- Rollback on any errors

### Performance:
- Only costs with `is_multiplied_by_learners = true` are processed
- Efficient queries using prepared statements
- Minimal database operations

### Error Handling:
- Graceful fallback if recalculation fails
- Detailed error logging
- User-friendly error messages

## Benefits

1. **Automatic Updates**: No manual intervention required
2. **Data Consistency**: All learner-multiplied costs stay synchronized
3. **Audit Trail**: Track when costs were last updated
4. **User Feedback**: Clear notifications about what was updated
5. **Cross-tab Sync**: Changes in one tab immediately affect other tabs
6. **Manual Override**: Admin can manually trigger recalculation if needed

## Future Enhancements

1. **Bulk Operations**: Select multiple costs for learner multiplication
2. **History Tracking**: Keep history of learner count changes
3. **Reporting**: Generate reports showing impact of learner changes
4. **Notifications**: Email notifications for significant changes
5. **Validation**: Prevent deletion of revenue if it would cause issues

This implementation ensures that your cost calculations always reflect the current number of learners, providing accurate financial tracking as your student population changes.

<?php
// /migrate_conditional_categories.php
// Migration script to add conditional category features to existing databases

require_once __DIR__ . '/config/db.php';

echo "<h1>Migrating Database for Conditional Categories</h1>\n";

try {
    // Check if include_in_cost column exists
    $result = $pdo->query("SHOW COLUMNS FROM categories LIKE 'include_in_cost'");
    if ($result->rowCount() == 0) {
        echo "<p>Adding include_in_cost column to categories table...</p>\n";
        $pdo->exec("ALTER TABLE categories ADD COLUMN include_in_cost BOOLEAN DEFAULT TRUE");
        echo "<p>✓ include_in_cost column added successfully.</p>\n";
    } else {
        echo "<p>✓ include_in_cost column already exists.</p>\n";
    }

    // Check if category_links table exists
    $result = $pdo->query("SHOW TABLES LIKE 'category_links'");
    if ($result->rowCount() == 0) {
        echo "<p>Creating category_links table...</p>\n";
        $sql = "CREATE TABLE category_links (
            id INT AUTO_INCREMENT PRIMARY KEY,
            multiplier_category_id INT NOT NULL,
            target_category_id INT NOT NULL,
            multiplier DECIMAL(10,2) NOT NULL DEFAULT 1.0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (multiplier_category_id) REFERENCES categories(id) ON DELETE CASCADE,
            FOREIGN KEY (target_category_id) REFERENCES categories(id) ON DELETE CASCADE,
            UNIQUE KEY unique_link (multiplier_category_id, target_category_id)
        )";
        $pdo->exec($sql);
        echo "<p>✓ category_links table created successfully.</p>\n";
    } else {
        echo "<p>✓ category_links table already exists.</p>\n";
    }

    echo "<h2>Migration completed successfully!</h2>\n";
    echo "<p>Your database now supports conditional category inclusion with multiplier mechanisms.</p>\n";
    echo "<p><strong>New Features Available:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Categories can be excluded from cost calculations by default</li>\n";
    echo "<li>Excluded categories can be conditionally included via multiplier relationships</li>\n";
    echo "<li>Multiplier categories can apply factors to target category costs</li>\n";
    echo "<li>Enhanced cost calculation logic respects these new rules</li>\n";
    echo "</ul>\n";
    echo "<p><a href='admin/category_links.php'>Manage Category Links</a> | <a href='admin/categories.php'>Manage Categories</a></p>\n";

} catch (Exception $e) {
    echo "<p style='color: red;'>Error during migration: " . $e->getMessage() . "</p>\n";
    echo "<p>Please check your database connection and try again.</p>\n";
}
?>

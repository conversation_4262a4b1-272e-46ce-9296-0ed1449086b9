<?php
// /ajax/update_revenue.php
require_once __DIR__ . '/../config/db.php';
header('Content-Type: application/json');

$id = intval($_POST['id'] ?? 0);
$month_year = trim($_POST['month_year'] ?? '');
$student_type = trim($_POST['student_type'] ?? '');
$revenue_per_student = floatval($_POST['revenue_per_student'] ?? 0);
$total_students = intval($_POST['total_students'] ?? 0);

if ($id && $month_year && $student_type && $revenue_per_student > 0 && $total_students > 0) {
  try {
    // Begin transaction
    $pdo->beginTransaction();

    // Update the revenue record
    $stmt = $pdo->prepare("UPDATE revenue SET month_year = ?, student_type = ?, revenue_per_student = ?, total_students = ? WHERE id = ?");
    $stmt->execute([$month_year, $student_type, $revenue_per_student, $total_students, $id]);

    // Recalculate learner-multiplied costs
    include_once __DIR__ . '/recalculate_learner_costs_internal.php';
    $recalcResult = recalculateLearnerCosts($pdo);

    // Commit transaction
    $pdo->commit();

    $message = 'Revenue updated successfully.';
    if ($recalcResult['updated_costs'] > 0) {
        $message .= " Automatically updated {$recalcResult['updated_costs']} cost(s) to reflect new learner count.";
    }

    echo json_encode([
        'success' => true,
        'message' => $message,
        'recalculation_result' => $recalcResult
    ]);
  } catch (Exception $e) {
    // Rollback on error
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    echo json_encode(['success' => false, 'message' => 'Error updating revenue: ' . $e->getMessage()]);
  }
} else {
  echo json_encode(['success' => false, 'message' => 'Invalid input for revenue update.']);
}
?>
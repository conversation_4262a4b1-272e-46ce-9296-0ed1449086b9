<?php
// /test_card_layout.php
// Test page to showcase improved card-footer layouts
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Card Layout Test - Cost Calculator</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            margin: 40px 0;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 Card Layout Test</h1>
        <p>Testing the improved card-footer layouts for different content types.</p>

        <div class="test-section">
            <h2>Dashboard Cards (Simple Footer)</h2>
            <div class="dashboard-grid">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Total Revenue</h3>
                        <div class="card-icon">
                            <i class="fas fa-coins"></i>
                        </div>
                    </div>
                    <div class="card-value">R125,450.00</div>
                    <p class="card-subtitle">All time revenue</p>
                    <div class="card-footer">
                        <span>Last updated: Today</span>
                        <div class="card-trend positive">
                            <i class="fas fa-arrow-up"></i>
                            +12.5%
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Total Costs</h3>
                        <div class="card-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                    </div>
                    <div class="card-value">R89,320.00</div>
                    <p class="card-subtitle">All time costs</p>
                    <div class="card-footer">
                        <span>Last updated: Today</span>
                        <div class="card-trend negative">
                            <i class="fas fa-arrow-down"></i>
                            -3.2%
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Net Profit</h3>
                        <div class="card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                    <div class="card-value">R36,130.00</div>
                    <p class="card-subtitle">Revenue minus costs</p>
                    <div class="card-footer">
                        <span>Profit margin: 28.9%</span>
                        <div class="card-trend positive">
                            <i class="fas fa-arrow-up"></i>
                            +8.7%
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Category Cards (Complex Footer)</h2>
            <div class="test-grid">
                <div class="card category-block">
                    <div class="card-header">
                        <h3>
                            Training Materials
                            <span class="badge badge-warning" title="Excluded from cost calculations unless linked">
                                <i class="fas fa-link"></i> Conditional
                            </span>
                        </h3>
                        <button class="delete-category-btn" title="Delete Category">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>

                    <div class="card-body">
                        <p>Educational resources and training materials for learners.</p>
                        <p class="text-warning">
                            <i class="fas fa-info-circle"></i>
                            This category is excluded from cost calculations unless linked with a multiplier category.
                        </p>
                    </div>

                    <div class="card-footer">
                        <h4><i class="fas fa-layer-group"></i> Subcategories</h4>
                        <ul class="subcategory-list">
                            <li class="subcategory-item">
                                <div class="subcategory-content">
                                    <strong>Textbooks</strong>
                                    <br><small>Educational books and reference materials</small>
                                </div>
                                <button class="delete-subcategory-btn" title="Delete Subcategory">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </li>
                            <li class="subcategory-item">
                                <div class="subcategory-content">
                                    <strong>Online Courses</strong>
                                    <br><small>Digital learning platforms and courses</small>
                                </div>
                                <button class="delete-subcategory-btn" title="Delete Subcategory">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </li>
                            <li class="subcategory-item">
                                <div class="subcategory-content">
                                    <strong>Workshop Materials</strong>
                                </div>
                                <button class="delete-subcategory-btn" title="Delete Subcategory">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </li>
                        </ul>

                        <form class="subcategory-form">
                            <div class="form-group">
                                <input type="text" name="name" placeholder="Subcategory name" required />
                            </div>
                            <div class="form-group">
                                <textarea name="description" placeholder="Description"></textarea>
                            </div>
                            <button type="submit" class="btn btn-sm">
                                <i class="fas fa-plus"></i> Add Subcategory
                            </button>
                        </form>
                    </div>
                </div>

                <div class="card category-block">
                    <div class="card-header">
                        <h3>Accommodation</h3>
                        <button class="delete-category-btn" title="Delete Category">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>

                    <div class="card-body">
                        <p>Housing and accommodation costs for learners.</p>
                    </div>

                    <div class="card-footer">
                        <h4><i class="fas fa-layer-group"></i> Subcategories</h4>
                        <ul class="subcategory-list">
                            <!-- Empty list to test the "No subcategories yet" message -->
                        </ul>

                        <form class="subcategory-form">
                            <div class="form-group">
                                <input type="text" name="name" placeholder="Subcategory name" required />
                            </div>
                            <div class="form-group">
                                <textarea name="description" placeholder="Description"></textarea>
                            </div>
                            <button type="submit" class="btn btn-sm">
                                <i class="fas fa-plus"></i> Add Subcategory
                            </button>
                        </form>
                    </div>
                </div>

                <div class="card category-block">
                    <div class="card-header">
                        <h3>Transportation</h3>
                        <button class="delete-category-btn" title="Delete Category">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>

                    <div class="card-body">
                        <p>Travel and transportation expenses.</p>
                    </div>

                    <div class="card-footer">
                        <h4><i class="fas fa-layer-group"></i> Subcategories</h4>
                        <ul class="subcategory-list">
                            <li class="subcategory-item">
                                <div class="subcategory-content">
                                    <strong>Bus Tickets</strong>
                                    <br><small>Public transportation costs</small>
                                </div>
                                <button class="delete-subcategory-btn" title="Delete Subcategory">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </li>
                            <li class="subcategory-item">
                                <div class="subcategory-content">
                                    <strong>Taxi Fares</strong>
                                </div>
                                <button class="delete-subcategory-btn" title="Delete Subcategory">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </li>
                            <li class="subcategory-item">
                                <div class="subcategory-content">
                                    <strong>Fuel Costs</strong>
                                    <br><small>Vehicle fuel expenses</small>
                                </div>
                                <button class="delete-subcategory-btn" title="Delete Subcategory">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </li>
                        </ul>

                        <form class="subcategory-form">
                            <div class="form-group">
                                <input type="text" name="name" placeholder="Subcategory name" required />
                            </div>
                            <div class="form-group">
                                <textarea name="description" placeholder="Description"></textarea>
                            </div>
                            <button type="submit" class="btn btn-sm">
                                <i class="fas fa-plus"></i> Add Subcategory
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Navigation</h2>
            <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                <a href="admin/categories.php" class="btn btn-primary">
                    <i class="fas fa-tags"></i> View Real Categories
                </a>
                <a href="admin/costs.php" class="btn btn-secondary">
                    <i class="fas fa-money-bill-wave"></i> Costs Page
                </a>
                <a href="admin/analytics.php" class="btn btn-success">
                    <i class="fas fa-chart-bar"></i> Analytics Dashboard
                </a>
                <a href="index.php" class="btn btn-warning">
                    <i class="fas fa-home"></i> Home
                </a>
            </div>
        </div>
    </div>

    <script>
    // Add some interactivity for testing
    document.addEventListener('DOMContentLoaded', function() {
        // Test form submissions
        const forms = document.querySelectorAll('.subcategory-form');
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                const input = form.querySelector('input[name="name"]');
                const list = form.closest('.card-footer').querySelector('.subcategory-list');
                
                if (input.value.trim()) {
                    const li = document.createElement('li');
                    li.textContent = input.value.trim();
                    list.appendChild(li);
                    form.reset();
                }
            });
        });

        // Test delete category buttons
        const deleteButtons = document.querySelectorAll('.delete-category-btn');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                if (confirm('Delete this test category?')) {
                    button.closest('.card').style.opacity = '0.5';
                    setTimeout(() => {
                        button.closest('.card').remove();
                    }, 300);
                }
            });
        });

        // Test delete subcategory buttons
        const deleteSubcategoryButtons = document.querySelectorAll('.delete-subcategory-btn');
        deleteSubcategoryButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const subcategoryItem = button.closest('.subcategory-item');
                const subcategoryName = subcategoryItem.querySelector('strong').textContent;

                if (confirm(`Delete subcategory "${subcategoryName}"?`)) {
                    subcategoryItem.style.opacity = '0.5';
                    setTimeout(() => {
                        subcategoryItem.remove();
                    }, 300);
                }
            });
        });
    });
    </script>
</body>
</html>

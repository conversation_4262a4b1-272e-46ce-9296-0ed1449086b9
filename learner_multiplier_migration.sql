-- Database Migration: Add learner multiplier fields to costs table
-- Run this SQL script in your database if the PHP migration doesn't work

-- Check if columns already exist (optional - for information only)
-- SHOW COLUMNS FROM costs;

-- Add the new columns to the costs table
ALTER TABLE costs ADD COLUMN is_multiplied_by_learners BOOLEAN DEFAULT FALSE AFTER num_days;
ALTER TABLE costs ADD COLUMN original_amount_per_learner DECIMAL(10,2) NULL AFTER is_multiplied_by_learners;
ALTER TABLE costs ADD COLUMN learner_count_used INT NULL AFTER original_amount_per_learner;
ALTER TABLE costs ADD COLUMN last_learner_update TIMESTAMP NULL AFTER learner_count_used;

-- Verify the new structure
SHOW COLUMNS FROM costs;

-- Optional: Show a sample of the updated table structure
SELECT 'Migration completed successfully!' as status;

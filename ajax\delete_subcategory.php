<?php
// /ajax/delete_subcategory.php
session_start();
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../includes/auth.php';

// Ensure only admins can delete subcategories
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
  header('Content-Type: application/json');
  echo json_encode(['success' => false, 'message' => 'Unauthorized access.']);
  exit;
}

// Set content type to JSON
header('Content-Type: application/json');

// Debug information
$debug = [];
$debug['post_data'] = $_POST;
$debug['raw_input'] = file_get_contents('php://input');
$debug['session'] = $_SESSION;

try {
  // Get subcategory ID from either POST or JSON input
  $json_input = json_decode(file_get_contents('php://input'), true);
  $id = intval($_POST['id'] ?? $json_input['id'] ?? 0);
  $debug['subcategory_id'] = $id;
  $debug['json_input'] = $json_input;

  // Validate input
  if ($id <= 0) {
    throw new Exception('Valid subcategory ID is required.');
  }

  // Check if subcategory exists
  $check = $pdo->prepare("SELECT id FROM subcategories WHERE id = ?");
  $check->execute([$id]);
  if (!$check->fetch()) {
    throw new Exception('The subcategory does not exist.');
  }

  // Begin transaction to ensure all operations succeed or fail together
  $pdo->beginTransaction();

  try {
    // Delete any costs associated with this subcategory
    $stmt = $pdo->prepare("DELETE FROM costs WHERE subcategory_id = ?");
    $stmt->execute([$id]);

    // Delete the subcategory
    $stmt = $pdo->prepare("DELETE FROM subcategories WHERE id = ?");
    $result = $stmt->execute([$id]);

    if (!$result) {
      throw new Exception('Failed to delete subcategory from database.');
    }

    // Commit the transaction
    $pdo->commit();
  } catch (Exception $e) {
    // Rollback the transaction on error
    $pdo->rollBack();
    throw $e;
  }

  // Return success response
  echo json_encode([
    'success' => true,
    'message' => 'Subcategory deleted successfully.',
    'debug' => $debug
  ]);

} catch (Exception $e) {
  // Return error response
  echo json_encode([
    'success' => false,
    'message' => $e->getMessage(),
    'debug' => $debug
  ]);
}

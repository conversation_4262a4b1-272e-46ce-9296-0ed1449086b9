<?php
// /viewer/viewer_dashboard.php
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/cost_calculator.php';

// Role-based guard for viewer
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'viewer') {
  header('Location: ../auth/login.php');
  exit;
}

// Fetch data with error handling
try {
    $costs = $pdo->query("SELECT co.*, ca.name AS category, su.name AS subcategory FROM costs co LEFT JOIN categories ca ON co.category_id = ca.id LEFT JOIN subcategories su ON co.subcategory_id = su.id ORDER BY co.created_at DESC")->fetchAll();
} catch (Exception $e) {
    $costs = [];
}

try {
    $revenues = $pdo->query("SELECT * FROM revenue ORDER BY created_at DESC")->fetchAll();
} catch (Exception $e) {
    $revenues = [];
}

// Analytics data calculations
try {
    $totalRevenue = $pdo->query("SELECT SUM(revenue_per_student * total_students) as total FROM revenue")->fetch()['total'] ?? 0;
    $totalCosts = calculateTotalCosts($pdo);
} catch (Exception $e) {
    $totalRevenue = 0;
    $totalCosts = 0;
}

$netProfit = $totalRevenue - $totalCosts;
$profitMargin = ($totalRevenue > 0) ? round(($netProfit / $totalRevenue) * 100, 2) : 0;

// Get revenue by month
try {
    $revenueByMonthResult = $pdo->query("
        SELECT
            DATE_FORMAT(month_year, '%Y-%m') as month,
            SUM(revenue_per_student * total_students) as total
        FROM revenue
        GROUP BY month
        ORDER BY month
    ")->fetchAll();
} catch (Exception $e) {
    $revenueByMonthResult = [];
}

$revenueByMonth = [];
foreach ($revenueByMonthResult as $row) {
    $revenueByMonth[$row['month']] = $row['total'];
}

// Get costs by month using new calculation logic
try {
    // Get all unique months from costs
    $monthsResult = $pdo->query("
        SELECT DISTINCT DATE_FORMAT(created_at, '%Y-%m') as month
        FROM costs
        ORDER BY month
    ")->fetchAll();

    $costsByMonth = [];
    foreach ($monthsResult as $row) {
        $month = $row['month'];
        $costsByMonth[$month] = calculateTotalCosts($pdo, $month);
    }
} catch (Exception $e) {
    $costsByMonth = [];
}

// Combine months
$allMonths = array_unique(array_merge(array_keys($revenueByMonth), array_keys($costsByMonth)));
sort($allMonths);

// Format months for display
$formattedMonths = [];
$revenueData = [];
$costsData = [];
$profitData = [];

// Ensure we have at least some data for the charts
if (empty($allMonths)) {
    $formattedMonths = [date('M Y')];
    $revenueData = [0];
    $costsData = [0];
    $profitData = [0];
} else {
    foreach ($allMonths as $month) {
        $date = new DateTime($month . '-01');
        $formattedMonths[] = $date->format('M Y');

        $rev = $revenueByMonth[$month] ?? 0;
        $cost = $costsByMonth[$month] ?? 0;

        $revenueData[] = floatval($rev);
        $costsData[] = floatval($cost);
        $profitData[] = floatval($rev - $cost);
    }
}

// Get cost breakdown by category
try {
    $costsByCategory = getCostBreakdownByCategory($pdo);
} catch (Exception $e) {
    $costsByCategory = [];
}

if (empty($costsByCategory)) {
    $costsByCategory = [['category' => 'No Data', 'total' => 0]];
}

// Get revenue by student type
try {
    $revenueByType = $pdo->query("
        SELECT
            student_type,
            SUM(revenue_per_student * total_students) as total
        FROM revenue
        GROUP BY student_type
    ")->fetchAll();
} catch (Exception $e) {
    $revenueByType = [];
}

if (empty($revenueByType)) {
    $revenueByType = [['student_type' => 'No Data', 'total' => 0]];
}
?>

  <main class="container">
    <section id="costs" class="tab">
      <h1 class="section-title"><i class="fas fa-money-bill-wave"></i> Cost Entries</h1>



      <?php if (empty($costs)): ?>
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">No Cost Data Available</h3>
          </div>
          <p class="card-subtitle">No cost entries have been added yet.</p>
        </div>
      <?php else: ?>
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Cost Overview</h3>
          </div>
          <input type="text" placeholder="Search costs..." class="table-search" data-target="cost-table">
          <div class="table-container">
            <table id="cost-table" class="data-table">
              <thead>
                <tr>
                  <th>Description</th>
                  <th>Amount</th>
                  <th>Category</th>
                  <th>Subcategory</th>
                  <th>Rate Type</th>
                  <th>Days</th>
                  <th>Total</th>
                </tr>
              </thead>
              <tbody>
                <?php foreach ($costs as $row): ?>
                <tr>
                  <td><?= htmlspecialchars($row['description']) ?></td>
                  <td>R<?= number_format($row['amount'], 2) ?></td>
                  <td><?= htmlspecialchars($row['category'] ?? 'N/A') ?></td>
                  <td><?= htmlspecialchars($row['subcategory'] ?? 'N/A') ?></td>
                  <td><?= ucfirst($row['rate_type']) ?></td>
                  <td><?= $row['num_days'] ?></td>
                  <td>R<?= number_format($row['amount'] * $row['num_days'], 2) ?></td>
                </tr>
                <?php endforeach; ?>
              </tbody>
            </table>
          </div>
        </div>
      <?php endif; ?>
    </section>

    <section id="revenue" class="tab">
      <h1 class="section-title"><i class="fas fa-coins"></i> Revenue Records</h1>

      <?php if (empty($revenues)): ?>
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">No Revenue Data Available</h3>
          </div>
          <p class="card-subtitle">No revenue records have been added yet.</p>
        </div>
      <?php else: ?>
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Revenue Overview</h3>
          </div>
          <input type="text" placeholder="Search revenue..." class="table-search" data-target="revenue-table">
          <div class="table-container">
            <table id="revenue-table" class="data-table">
              <thead>
                <tr>
                  <th>Month</th>
                  <th>Student Type</th>
                  <th>Revenue per Student</th>
                  <th>Total Students</th>
                  <th>Total Revenue</th>
                </tr>
              </thead>
              <tbody>
                <?php foreach ($revenues as $rev): ?>
                <tr>
                  <td><?= date('F Y', strtotime($rev['month_year'] . '-01')) ?></td>
                  <td><?= htmlspecialchars($rev['student_type']) ?></td>
                  <td>R<?= number_format($rev['revenue_per_student'], 2) ?></td>
                  <td><?= $rev['total_students'] ?></td>
                  <td>R<?= number_format($rev['revenue_per_student'] * $rev['total_students'], 2) ?></td>
                </tr>
                <?php endforeach; ?>
              </tbody>
            </table>
          </div>
        </div>
      <?php endif; ?>
    </section>

    <section id="analytics" class="tab">
      <h1 class="section-title"><i class="fas fa-chart-bar"></i> Analytics Dashboard</h1>

      <!-- Summary Cards -->
      <div class="dashboard-grid">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Total Revenue</h3>
            <div class="card-icon">
              <i class="fas fa-coins"></i>
            </div>
          </div>
          <div class="card-value">R<?= number_format($totalRevenue, 2) ?></div>
          <p class="card-subtitle">All time revenue</p>
        </div>

        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Total Costs</h3>
            <div class="card-icon">
              <i class="fas fa-money-bill-wave"></i>
            </div>
          </div>
          <div class="card-value">R<?= number_format($totalCosts, 2) ?></div>
          <p class="card-subtitle">All time costs</p>
        </div>

        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Net Profit</h3>
            <div class="card-icon">
              <i class="fas fa-chart-line"></i>
            </div>
          </div>
          <div class="card-value" style="color: <?= $netProfit >= 0 ? 'var(--success)' : 'var(--warning)' ?>">
            R<?= number_format($netProfit, 2) ?>
          </div>
          <p class="card-subtitle">Revenue - Costs</p>
        </div>

        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Profit Margin</h3>
            <div class="card-icon">
              <i class="fas fa-percentage"></i>
            </div>
          </div>
          <div class="card-value" style="color: <?= $profitMargin >= 0 ? 'var(--success)' : 'var(--warning)' ?>">
            <?= $profitMargin ?>%
          </div>
          <p class="card-subtitle">Profit as % of Revenue</p>
        </div>
      </div>

      <!-- Charts -->
      <div class="card">
        <div class="card-header">
          <h3 class="card-title"><i class="fas fa-chart-line"></i> Revenue vs Costs Over Time</h3>
        </div>
        <canvas id="revenueChart" width="400" height="200"></canvas>
      </div>

      <div class="chart-grid">
        <div class="card chart-container">
          <div class="card-header">
            <h3 class="card-title"><i class="fas fa-chart-pie"></i> Cost Breakdown by Category</h3>
          </div>
          <canvas id="costChart" width="400" height="300"></canvas>
        </div>

        <div class="card chart-container">
          <div class="card-header">
            <h3 class="card-title"><i class="fas fa-users"></i> Revenue by Student Type</h3>
          </div>
          <canvas id="studentTypeChart" width="400" height="300"></canvas>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <h3 class="card-title"><i class="fas fa-chart-bar"></i> Profitability Analysis</h3>
        </div>
        <canvas id="profitabilityChart" width="400" height="200"></canvas>
      </div>
    </section>
  </main>

  <?php require_once __DIR__ . '/../includes/footer.php'; ?>



  <script>
    // Tab navigation
    document.addEventListener("DOMContentLoaded", () => {
      // Handle tab navigation
      const navLinks = document.querySelectorAll('.nav-menu a[href^="#"]');
      const sections = document.querySelectorAll('.tab');

      function showTab(targetId) {
        // Hide all sections
        sections.forEach(section => {
          section.style.display = 'none';
        });

        // Remove active class from all nav links
        navLinks.forEach(link => {
          link.classList.remove('active');
        });

        // Show target section
        const targetSection = document.querySelector(targetId);
        if (targetSection) {
          targetSection.style.display = 'block';
        }

        // Add active class to clicked nav link
        const activeLink = document.querySelector(`a[href="${targetId}"]`);
        if (activeLink) {
          activeLink.classList.add('active');
        }
      }

      // Handle nav link clicks
      navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
          e.preventDefault();
          const targetId = link.getAttribute('href');
          showTab(targetId);

          // Update URL hash without scrolling
          history.pushState(null, null, targetId);
        });
      });

      // Show initial tab based on URL hash or default to costs
      const initialTab = window.location.hash || '#costs';
      showTab(initialTab);

      // Handle table search
      document.querySelectorAll(".table-search").forEach(input => {
        input.addEventListener("input", () => {
          const targetId = input.dataset.target;
          const filter = input.value.toLowerCase();
          document.querySelectorAll(`#${targetId} tbody tr`).forEach(row => {
            row.style.display = [...row.children].some(td =>
              td.textContent.toLowerCase().includes(filter)
            ) ? "" : "none";
          });
        });
      });

      // Initialize charts
      initializeCharts();
    });

    // Chart initialization
    function initializeCharts() {
      // Data from PHP
      const months = <?= json_encode($formattedMonths) ?>;
      const revenueData = <?= json_encode($revenueData) ?>;
      const costsData = <?= json_encode($costsData) ?>;
      const profitData = <?= json_encode($profitData) ?>;

      const costCategories = <?= json_encode(array_column($costsByCategory, 'category')) ?>;
      const costAmounts = <?= json_encode(array_map('floatval', array_column($costsByCategory, 'total'))) ?>;

      const revenueTypes = <?= json_encode(array_column($revenueByType, 'student_type')) ?>;
      const revenueAmounts = <?= json_encode(array_map('floatval', array_column($revenueByType, 'total'))) ?>;

      // Revenue vs Costs Chart
      const revenueCtx = document.getElementById('revenueChart');
      if (revenueCtx) {
        new Chart(revenueCtx, {
          type: 'line',
          data: {
            labels: months,
            datasets: [{
              label: 'Revenue',
              data: revenueData,
              borderColor: 'rgb(34, 197, 94)',
              backgroundColor: 'rgba(34, 197, 94, 0.1)',
              tension: 0.4
            }, {
              label: 'Costs',
              data: costsData,
              borderColor: 'rgb(239, 68, 68)',
              backgroundColor: 'rgba(239, 68, 68, 0.1)',
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            plugins: {
              title: {
                display: true,
                text: 'Revenue vs Costs Over Time'
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  callback: function(value) {
                    return 'R' + value.toLocaleString();
                  }
                }
              }
            }
          }
        });
      }

      // Cost Breakdown Chart
      const costCtx = document.getElementById('costChart');
      if (costCtx) {
        new Chart(costCtx, {
          type: 'doughnut',
          data: {
            labels: costCategories,
            datasets: [{
              data: costAmounts,
              backgroundColor: [
                'rgb(59, 130, 246)',
                'rgb(16, 185, 129)',
                'rgb(245, 158, 11)',
                'rgb(239, 68, 68)',
                'rgb(139, 92, 246)',
                'rgb(236, 72, 153)'
              ]
            }]
          },
          options: {
            responsive: true,
            plugins: {
              title: {
                display: true,
                text: 'Cost Breakdown by Category'
              }
            }
          }
        });
      }

      // Revenue by Student Type Chart
      const studentCtx = document.getElementById('studentTypeChart');
      if (studentCtx) {
        new Chart(studentCtx, {
          type: 'pie',
          data: {
            labels: revenueTypes,
            datasets: [{
              data: revenueAmounts,
              backgroundColor: [
                'rgb(34, 197, 94)',
                'rgb(59, 130, 246)',
                'rgb(245, 158, 11)',
                'rgb(139, 92, 246)'
              ]
            }]
          },
          options: {
            responsive: true,
            plugins: {
              title: {
                display: true,
                text: 'Revenue by Student Type'
              }
            }
          }
        });
      }

      // Profitability Chart
      const profitCtx = document.getElementById('profitabilityChart');
      if (profitCtx) {
        new Chart(profitCtx, {
          type: 'bar',
          data: {
            labels: months,
            datasets: [{
              label: 'Profit/Loss',
              data: profitData,
              backgroundColor: profitData.map(value =>
                value >= 0 ? 'rgba(34, 197, 94, 0.8)' : 'rgba(239, 68, 68, 0.8)'
              ),
              borderColor: profitData.map(value =>
                value >= 0 ? 'rgb(34, 197, 94)' : 'rgb(239, 68, 68)'
              ),
              borderWidth: 1
            }]
          },
          options: {
            responsive: true,
            plugins: {
              title: {
                display: true,
                text: 'Monthly Profitability'
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  callback: function(value) {
                    return 'R' + value.toLocaleString();
                  }
                }
              }
            }
          }
        });
      }
    }
  </script>
</body>
</html>

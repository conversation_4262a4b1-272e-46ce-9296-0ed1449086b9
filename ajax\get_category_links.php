<?php
// /ajax/get_category_links.php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../includes/auth.php';

// Ensure user is authenticated as admin
authenticate_admin();

header('Content-Type: application/json');

try {
  // Fetch all category links with category names
  $stmt = $pdo->query("
    SELECT 
      cl.id,
      cl.multiplier_category_id,
      cl.target_category_id,
      cl.multiplier,
      cl.created_at,
      mc.name as multiplier_category_name,
      tc.name as target_category_name
    FROM category_links cl
    LEFT JOIN categories mc ON cl.multiplier_category_id = mc.id
    LEFT JOIN categories tc ON cl.target_category_id = tc.id
    ORDER BY mc.name, tc.name
  ");
  
  $links = $stmt->fetchAll(PDO::FETCH_ASSOC);

  // Return success response
  echo json_encode([
    'success' => true,
    'links' => $links
  ]);

} catch (Exception $e) {
  // Return error response
  echo json_encode([
    'success' => false,
    'message' => $e->getMessage()
  ]);
}
?>

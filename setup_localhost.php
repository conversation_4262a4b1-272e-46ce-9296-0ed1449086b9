<?php
// /setup_localhost.php
// Quick setup script for localhost development

// Force development environment
$_SERVER['SERVER_NAME'] = 'localhost';
$_SERVER['HTTP_HOST'] = 'localhost';

require_once __DIR__ . '/config/environment.php';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Localhost Setup - Cost Calculator</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .step { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 10px 0; }
        code { background: #f8f9fa; padding: 2px 5px; border-radius: 3px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>";

echo "<h1>🚀 Cost Calculator - Localhost Setup</h1>";

echo "<div class='info'>
<strong>Current Environment:</strong> " . getCurrentEnvironment() . "<br>
<strong>Database Host:</strong> $host<br>
<strong>Database Name:</strong> $db<br>
<strong>Username:</strong> $user
</div>";

// Check if we're in development mode
if (!isDevelopment()) {
    echo "<div class='error'>
    <h3>❌ Not in Development Mode</h3>
    <p>This setup script is only for localhost development. Please ensure you're running this on localhost.</p>
    </div>";
    exit;
}

echo "<h2>📋 Setup Steps</h2>";

// Step 1: Check MySQL connection
echo "<div class='step'>
<h3>Step 1: Testing MySQL Connection</h3>";

try {
    $testConnection = new PDO("mysql:host=$host;charset=$charset", $user, $pass);
    $testConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div class='success'>✅ MySQL connection successful!</div>";
    
    // Step 2: Create database if it doesn't exist
    echo "<h3>Step 2: Creating Database</h3>";
    $testConnection->exec("CREATE DATABASE IF NOT EXISTS `$db`");
    echo "<div class='success'>✅ Database '$db' created/verified!</div>";
    
    // Step 3: Connect to the specific database
    echo "<h3>Step 3: Connecting to Database</h3>";
    $pdo = new PDO("mysql:host=$host;dbname=$db;charset=$charset", $user, $pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    echo "<div class='success'>✅ Connected to database '$db'!</div>";
    
    // Step 4: Run table creation
    echo "<h3>Step 4: Creating Tables</h3>";
    
    // Include the setup logic
    require_once __DIR__ . '/setup.php';
    
} catch (PDOException $e) {
    echo "<div class='error'>
    <h3>❌ Database Connection Failed</h3>
    <p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
    
    <h4>🔧 How to Fix:</h4>
    <ol>
        <li><strong>Make sure XAMPP/WAMP is running:</strong>
            <ul>
                <li>Start Apache and MySQL services</li>
                <li>Check that MySQL is running on port 3306</li>
            </ul>
        </li>
        <li><strong>Check MySQL credentials:</strong>
            <ul>
                <li>Default XAMPP: username='root', password='' (empty)</li>
                <li>Default WAMP: username='root', password='' (empty)</li>
                <li>If you've set a password, update <code>config/environment.php</code></li>
            </ul>
        </li>
        <li><strong>Test phpMyAdmin:</strong>
            <ul>
                <li>Go to <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></li>
                <li>If it works, the MySQL connection should work too</li>
            </ul>
        </li>
    </ol>
    </div>";
}

echo "</div>";

echo "<h2>🔗 Quick Links</h2>
<ul>
    <li><a href='http://localhost/phpmyadmin' target='_blank'>📊 phpMyAdmin</a> - Database management</li>
    <li><a href='admin/dashboard.php'>🏠 Admin Dashboard</a> - Start using the application</li>
    <li><a href='auth/login.php'>🔐 Login Page</a> - Login (admin/admin123)</li>
    <li><a href='migrate_conditional_categories.php'>🔄 Run Migration</a> - Apply conditional categories feature</li>
</ul>";

echo "<h2>⚙️ Environment Configuration</h2>
<div class='info'>
<p>To switch between development and production:</p>
<ol>
    <li>Edit <code>config/environment.php</code></li>
    <li>Change <code>\$ENVIRONMENT</code> variable:
        <ul>
            <li><code>'development'</code> - for localhost</li>
            <li><code>'production'</code> - for InfinityFree</li>
            <li><code>'auto'</code> - automatic detection (recommended)</li>
        </ul>
    </li>
</ol>
</div>";

echo "<h2>📝 Default Login</h2>
<div class='step'>
<strong>Username:</strong> admin<br>
<strong>Password:</strong> admin123
</div>";

echo "<h2>🆕 New Features</h2>
<div class='info'>
<p>This version includes the new <strong>Conditional Category Inclusion</strong> system:</p>
<ul>
    <li>Categories can be excluded from cost calculations by default</li>
    <li>Excluded categories can be conditionally included via multiplier relationships</li>
    <li>Perfect for scenarios like weekend work uplifts, overtime calculations, etc.</li>
    <li>Access via <strong>Admin → Category Links</strong></li>
</ul>
</div>";

echo "</body></html>";
?>

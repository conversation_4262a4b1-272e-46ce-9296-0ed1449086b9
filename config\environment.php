<?php
// /config/environment.php
// Environment configuration - Easy switching between development and production

// ========================================
// ENVIRONMENT SETTINGS
// ========================================

// Set to 'development' for localhost or 'production' for InfinityFree
// You can also set to 'auto' to let the system detect automatically
$ENVIRONMENT = 'auto'; // Options: 'auto', 'development', 'production'

// ========================================
// DATABASE CONFIGURATIONS
// ========================================

$config = [
    'development' => [
        'host' => 'localhost',
        'database' => 'cost_calculator',
        'username' => 'root',
        'password' => '', // Usually empty for XAMPP/WAMP
        'charset' => 'utf8mb4',
        'debug' => true,
        'display_errors' => true
    ],
    
    'production' => [
        'host' => 'sql307.infinityfree.com',
        'database' => 'if0_39163993_cost_calculator',
        'username' => 'if0_39163993',
        'password' => 'sV5RSK88Jk5',
        'charset' => 'utf8mb4',
        'debug' => false,
        'display_errors' => false
    ]
];

// ========================================
// ENVIRONMENT DETECTION
// ========================================

if ($ENVIRONMENT === 'auto') {
    // Auto-detect environment based on server characteristics
    $isLocalhost = (
        $_SERVER['SERVER_NAME'] === 'localhost' || 
        $_SERVER['SERVER_NAME'] === '127.0.0.1' || 
        strpos($_SERVER['SERVER_NAME'], 'localhost') !== false ||
        $_SERVER['HTTP_HOST'] === 'localhost' ||
        strpos($_SERVER['HTTP_HOST'], 'localhost') !== false ||
        strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false ||
        strpos($_SERVER['HTTP_HOST'], '.local') !== false
    );
    
    $currentEnvironment = $isLocalhost ? 'development' : 'production';
} else {
    $currentEnvironment = $ENVIRONMENT;
}

// Get current configuration
$currentConfig = $config[$currentEnvironment];

// Set PHP error reporting based on environment
if ($currentConfig['display_errors']) {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    error_reporting(0);
}

// ========================================
// EXPORT CONFIGURATION
// ========================================

// Export variables for use in db.php
$host = $currentConfig['host'];
$db = $currentConfig['database'];
$user = $currentConfig['username'];
$pass = $currentConfig['password'];
$charset = $currentConfig['charset'];
$debug = $currentConfig['debug'];

// Optional: Log which environment is being used
if ($debug) {
    error_log("Calculator running in $currentEnvironment mode on $host");
}

// ========================================
// HELPER FUNCTIONS
// ========================================

function getCurrentEnvironment() {
    global $currentEnvironment;
    return $currentEnvironment;
}

function isProduction() {
    return getCurrentEnvironment() === 'production';
}

function isDevelopment() {
    return getCurrentEnvironment() === 'development';
}

function getConfig($key = null) {
    global $currentConfig;
    if ($key === null) {
        return $currentConfig;
    }
    return isset($currentConfig[$key]) ? $currentConfig[$key] : null;
}
?>

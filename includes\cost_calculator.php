<?php
// /includes/cost_calculator.php
// Helper functions for calculating costs with conditional inclusion and multipliers

/**
 * Calculate total costs respecting include_in_cost flag and category links
 * @param PDO $pdo Database connection
 * @param string|null $dateFilter Optional date filter (e.g., '2024-01' for specific month)
 * @return float Total calculated cost
 */
function calculateTotalCosts($pdo, $dateFilter = null) {
    try {
        // Build date filter condition
        $dateCondition = '';
        $params = [];
        if ($dateFilter) {
            $dateCondition = " AND DATE_FORMAT(co.created_at, '%Y-%m') = ?";
            $params[] = $dateFilter;
        }

        // Get costs from categories that are included in cost calculations
        $includedCostsQuery = "
            SELECT SUM(co.amount * co.num_days) as total
            FROM costs co
            JOIN categories c ON co.category_id = c.id
            WHERE c.include_in_cost = 1" . $dateCondition;
        
        $stmt = $pdo->prepare($includedCostsQuery);
        $stmt->execute($params);
        $includedCosts = floatval($stmt->fetch()['total'] ?? 0);

        // Get multiplier costs from category links
        $multiplierCosts = calculateMultiplierCosts($pdo, $dateFilter);

        return $includedCosts + $multiplierCosts;
    } catch (Exception $e) {
        error_log("Error calculating total costs: " . $e->getMessage());
        return 0;
    }
}

/**
 * Calculate costs from category links (multiplier mechanism)
 * @param PDO $pdo Database connection
 * @param string|null $dateFilter Optional date filter
 * @return float Total multiplier costs
 */
function calculateMultiplierCosts($pdo, $dateFilter = null) {
    try {
        // Build date filter condition
        $dateCondition = '';
        $params = [];
        if ($dateFilter) {
            $dateCondition = " AND DATE_FORMAT(co.created_at, '%Y-%m') = ?";
            $params[] = $dateFilter;
        }

        // Get all category links
        $linksQuery = "
            SELECT 
                cl.multiplier_category_id,
                cl.target_category_id,
                cl.multiplier
            FROM category_links cl";
        
        $linksStmt = $pdo->query($linksQuery);
        $links = $linksStmt->fetchAll();

        $totalMultiplierCosts = 0;

        foreach ($links as $link) {
            // Check if the multiplier category has any costs
            $multiplierCategoryQuery = "
                SELECT COUNT(*) as count
                FROM costs co
                WHERE co.category_id = ?" . $dateCondition;
            
            $checkParams = [$link['multiplier_category_id']];
            if ($dateFilter) {
                $checkParams[] = $dateFilter;
            }
            
            $checkStmt = $pdo->prepare($multiplierCategoryQuery);
            $checkStmt->execute($checkParams);
            $hasMultiplierCosts = $checkStmt->fetch()['count'] > 0;

            // Only apply multiplier if the multiplier category has costs
            if ($hasMultiplierCosts) {
                // Get total costs from target category
                $targetCostsQuery = "
                    SELECT SUM(co.amount * co.num_days) as total
                    FROM costs co
                    WHERE co.category_id = ?" . $dateCondition;
                
                $targetParams = [$link['target_category_id']];
                if ($dateFilter) {
                    $targetParams[] = $dateFilter;
                }
                
                $targetStmt = $pdo->prepare($targetCostsQuery);
                $targetStmt->execute($targetParams);
                $targetCosts = floatval($targetStmt->fetch()['total'] ?? 0);

                // Apply multiplier
                $multipliedCosts = $targetCosts * floatval($link['multiplier']);
                $totalMultiplierCosts += $multipliedCosts;
            }
        }

        return $totalMultiplierCosts;
    } catch (Exception $e) {
        error_log("Error calculating multiplier costs: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get cost breakdown by category with conditional inclusion
 * @param PDO $pdo Database connection
 * @param string|null $dateFilter Optional date filter
 * @return array Array of categories with their calculated costs
 */
function getCostBreakdownByCategory($pdo, $dateFilter = null) {
    try {
        $breakdown = [];
        
        // Build date filter condition
        $dateCondition = '';
        $params = [];
        if ($dateFilter) {
            $dateCondition = " AND DATE_FORMAT(co.created_at, '%Y-%m') = ?";
            $params[] = $dateFilter;
        }

        // Get costs from included categories
        $includedQuery = "
            SELECT 
                c.id,
                c.name as category,
                SUM(co.amount * co.num_days) as total
            FROM costs co
            JOIN categories c ON co.category_id = c.id
            WHERE c.include_in_cost = 1" . $dateCondition . "
            GROUP BY c.id, c.name
            ORDER BY total DESC";
        
        $stmt = $pdo->prepare($includedQuery);
        $stmt->execute($params);
        $includedCategories = $stmt->fetchAll();

        foreach ($includedCategories as $cat) {
            $breakdown[] = [
                'category' => $cat['category'],
                'total' => floatval($cat['total']),
                'type' => 'included'
            ];
        }

        // Get multiplier costs grouped by multiplier category
        $multiplierBreakdown = getMultiplierCostBreakdown($pdo, $dateFilter);
        $breakdown = array_merge($breakdown, $multiplierBreakdown);

        return $breakdown;
    } catch (Exception $e) {
        error_log("Error getting cost breakdown: " . $e->getMessage());
        return [];
    }
}

/**
 * Get multiplier cost breakdown
 * @param PDO $pdo Database connection
 * @param string|null $dateFilter Optional date filter
 * @return array Array of multiplier categories with their calculated costs
 */
function getMultiplierCostBreakdown($pdo, $dateFilter = null) {
    try {
        $breakdown = [];
        
        // Build date filter condition
        $dateCondition = '';
        $params = [];
        if ($dateFilter) {
            $dateCondition = " AND DATE_FORMAT(co.created_at, '%Y-%m') = ?";
            $params[] = $dateFilter;
        }

        // Get category links with names
        $linksQuery = "
            SELECT 
                cl.multiplier_category_id,
                cl.target_category_id,
                cl.multiplier,
                mc.name as multiplier_category_name,
                tc.name as target_category_name
            FROM category_links cl
            JOIN categories mc ON cl.multiplier_category_id = mc.id
            JOIN categories tc ON cl.target_category_id = tc.id";
        
        $linksStmt = $pdo->query($linksQuery);
        $links = $linksStmt->fetchAll();

        $multiplierTotals = [];

        foreach ($links as $link) {
            // Check if the multiplier category has any costs
            $multiplierCategoryQuery = "
                SELECT COUNT(*) as count
                FROM costs co
                WHERE co.category_id = ?" . $dateCondition;
            
            $checkParams = [$link['multiplier_category_id']];
            if ($dateFilter) {
                $checkParams[] = $dateFilter;
            }
            
            $checkStmt = $pdo->prepare($multiplierCategoryQuery);
            $checkStmt->execute($checkParams);
            $hasMultiplierCosts = $checkStmt->fetch()['count'] > 0;

            if ($hasMultiplierCosts) {
                // Get target category costs
                $targetCostsQuery = "
                    SELECT SUM(co.amount * co.num_days) as total
                    FROM costs co
                    WHERE co.category_id = ?" . $dateCondition;
                
                $targetParams = [$link['target_category_id']];
                if ($dateFilter) {
                    $targetParams[] = $dateFilter;
                }
                
                $targetStmt = $pdo->prepare($targetCostsQuery);
                $targetStmt->execute($targetParams);
                $targetCosts = floatval($targetStmt->fetch()['total'] ?? 0);

                $multipliedCosts = $targetCosts * floatval($link['multiplier']);
                
                // Group by multiplier category
                $categoryName = $link['multiplier_category_name'] . ' (via ' . $link['target_category_name'] . ')';
                if (!isset($multiplierTotals[$categoryName])) {
                    $multiplierTotals[$categoryName] = 0;
                }
                $multiplierTotals[$categoryName] += $multipliedCosts;
            }
        }

        foreach ($multiplierTotals as $categoryName => $total) {
            $breakdown[] = [
                'category' => $categoryName,
                'total' => $total,
                'type' => 'multiplier'
            ];
        }

        return $breakdown;
    } catch (Exception $e) {
        error_log("Error getting multiplier cost breakdown: " . $e->getMessage());
        return [];
    }
}
?>

<?php
// /add_include_in_cost_column.php
// Quick migration to add the include_in_cost column to existing databases

require_once __DIR__ . '/config/environment.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Include In Cost Column - Migration</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔧 Add Include In Cost Column</h1>
    
    <div class="info">
        <p><strong>Environment:</strong> <?= getCurrentEnvironment() ?></p>
        <p><strong>Database:</strong> <?= htmlspecialchars($db) ?></p>
        <p>This migration adds the <code>include_in_cost</code> column to the categories table.</p>
    </div>

    <?php
    try {
        // Connect to database
        $pdo = new PDO("mysql:host=$host;dbname=$db;charset=$charset", $user, $pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='success'>✅ Connected to database successfully.</div>";
        
        // Check if column already exists
        $stmt = $pdo->query("SHOW COLUMNS FROM categories LIKE 'include_in_cost'");
        $columnExists = $stmt->rowCount() > 0;
        
        if ($columnExists) {
            echo "<div class='warning'>⚠️ Column 'include_in_cost' already exists in categories table.</div>";
            echo "<div class='info'>No migration needed. Your database is up to date!</div>";
        } else {
            echo "<div class='info'>📝 Adding 'include_in_cost' column to categories table...</div>";
            
            // Add the column
            $pdo->exec("ALTER TABLE categories ADD COLUMN include_in_cost BOOLEAN DEFAULT TRUE");
            
            echo "<div class='success'>✅ Column 'include_in_cost' added successfully!</div>";
            echo "<div class='info'>All existing categories have been set to include_in_cost = TRUE (default behavior).</div>";
        }
        
        // Check if category_links table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'category_links'");
        $tableExists = $stmt->rowCount() > 0;
        
        if (!$tableExists) {
            echo "<div class='info'>📝 Creating category_links table...</div>";
            
            $sql = "CREATE TABLE category_links (
                id INT AUTO_INCREMENT PRIMARY KEY,
                multiplier_category_id INT NOT NULL,
                target_category_id INT NOT NULL,
                multiplier DECIMAL(10,2) NOT NULL DEFAULT 1.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (multiplier_category_id) REFERENCES categories(id) ON DELETE CASCADE,
                FOREIGN KEY (target_category_id) REFERENCES categories(id) ON DELETE CASCADE,
                UNIQUE KEY unique_link (multiplier_category_id, target_category_id)
            )";
            
            $pdo->exec($sql);
            echo "<div class='success'>✅ Category links table created successfully!</div>";
        } else {
            echo "<div class='warning'>⚠️ Category links table already exists.</div>";
        }
        
        echo "<div class='success'>
        <h3>🎉 Migration Completed Successfully!</h3>
        <p>Your database now supports conditional category inclusion:</p>
        <ul>
            <li>✅ include_in_cost column added to categories table</li>
            <li>✅ category_links table created (if needed)</li>
            <li>✅ All existing categories set to include in cost calculations</li>
        </ul>
        </div>";
        
    } catch (PDOException $e) {
        echo "<div class='error'>
        <h3>❌ Migration Failed</h3>
        <p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        
        if (getCurrentEnvironment() === 'development') {
            echo "<h4>💡 Localhost Troubleshooting:</h4>
            <ul>
                <li>Make sure XAMPP/WAMP is running</li>
                <li>Check that MySQL service is started</li>
                <li>Verify database exists: <a href='http://localhost/phpmyadmin' target='_blank'>phpMyAdmin</a></li>
                <li>Try running <a href='setup_localhost.php'>localhost setup</a> first</li>
            </ul>";
        } else {
            echo "<h4>💡 Production Troubleshooting:</h4>
            <ul>
                <li>Verify database exists in your hosting control panel</li>
                <li>Check database credentials are correct</li>
                <li>Ensure you have ALTER TABLE permissions</li>
            </ul>";
        }
        
        echo "</div>";
    }
    ?>

    <h2>🎯 Next Steps</h2>
    <div class="info">
        <h4>Now you can:</h4>
        <ul>
            <li><a href="admin/categories.php">📂 Manage Categories</a> - Create categories with conditional inclusion</li>
            <li><a href="admin/category_links.php">🔗 Manage Category Links</a> - Set up multiplier relationships</li>
            <li><a href="admin/analytics.php">📊 View Analytics</a> - See enhanced cost calculations</li>
            <li><a href="auth/login.php">🔐 Login</a> - Access the application (admin/admin123)</li>
        </ul>
        
        <h4>New Features Available:</h4>
        <ul>
            <li><strong>Conditional Categories:</strong> Categories can be excluded from cost calculations by default</li>
            <li><strong>Multiplier Links:</strong> Excluded categories can be conditionally included with multipliers</li>
            <li><strong>Enhanced Analytics:</strong> Cost calculations respect the new conditional logic</li>
        </ul>
    </div>

    <h2>📖 How It Works</h2>
    <div class="info">
        <h4>Example: Weekend Work Uplift</h4>
        <ol>
            <li>Create "Labor Costs" category with <strong>include_in_cost = FALSE</strong></li>
            <li>Create "Weekend Work" category with <strong>include_in_cost = TRUE</strong></li>
            <li>Add costs to both categories</li>
            <li>Link "Weekend Work" → "Labor Costs" with multiplier 1.5</li>
            <li>When Weekend Work has costs, total includes Labor Costs × 1.5</li>
        </ol>
    </div>
</body>
</html>

<?php
// /ajax/save_cost.php
session_start();
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../includes/auth.php';
header('Content-Type: application/json');

// Debug: Log request to detect duplicates
error_log("Cost save request at: " . date('Y-m-d H:i:s') . " - Description: " . ($_POST['description'] ?? 'N/A'));

// Check authentication
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access.']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $description = trim($_POST['description'] ?? '');
    $amount = floatval($_POST['amount'] ?? 0);
    $category_id = intval($_POST['category_id'] ?? 0);
    $subcategory_id = intval($_POST['subcategory_id'] ?? 0);
    $rate_type = $_POST['rate_type'] ?? 'daily';
    $num_days = intval($_POST['num_days'] ?? 1);
    $submission_token = $_POST['submission_token'] ?? '';

    // Handle learner multiplier fields
    $learner_multiplier = intval($_POST['learner_multiplier'] ?? 0);
    $original_amount = floatval($_POST['original_amount'] ?? 0);
    $is_multiplied_by_learners = ($learner_multiplier > 0 && $original_amount > 0);

    // Enhanced duplicate prevention

    // 1. Check submission token if provided
    if (!empty($submission_token)) {
        if (!isset($_SESSION['used_tokens'])) {
            $_SESSION['used_tokens'] = [];
        }

        if (in_array($submission_token, $_SESSION['used_tokens'])) {
            echo json_encode([
                'success' => false,
                'message' => 'Duplicate submission token detected.'
            ]);
            exit;
        }

        // Mark token as used
        $_SESSION['used_tokens'][] = $submission_token;

        // Clean old tokens (keep only last 10)
        if (count($_SESSION['used_tokens']) > 10) {
            $_SESSION['used_tokens'] = array_slice($_SESSION['used_tokens'], -10);
        }
    }

    // 2. Check for identical submission in last 10 seconds
    $duplicateCheck = $pdo->prepare("
        SELECT id FROM costs
        WHERE description = ?
        AND amount = ?
        AND category_id = ?
        AND subcategory_id = ?
        AND rate_type = ?
        AND num_days = ?
        AND created_at > DATE_SUB(NOW(), INTERVAL 10 SECOND)
    ");
    $duplicateCheck->execute([$description, $amount, $category_id, $subcategory_id, $rate_type, $num_days]);

    if ($duplicateCheck->fetch()) {
        echo json_encode([
            'success' => false,
            'message' => 'Duplicate submission detected. Identical cost already exists within the last 10 seconds.'
        ]);
        exit;
    }

    // Validate input
    $errors = [];
    if (empty($description)) {
        $errors[] = "Description is required";
    }
    if ($amount <= 0) {
        $errors[] = "Amount must be greater than zero";
    }
    if ($category_id <= 0) {
        $errors[] = "Please select a category";
    }
    if ($subcategory_id <= 0) {
        $errors[] = "Please select a subcategory";
    }
    if (!in_array($rate_type, ['daily', 'monthly'])) {
        $errors[] = "Invalid rate type";
    }
    if ($num_days <= 0) {
        $errors[] = "Number of days must be greater than zero";
    }

    if (empty($errors)) {
        try {
            if ($is_multiplied_by_learners) {
                // Save cost with learner multiplier information
                $stmt = $pdo->prepare("INSERT INTO costs (description, amount, category_id, subcategory_id, rate_type, num_days, is_multiplied_by_learners, original_amount_per_learner, learner_count_used, last_learner_update) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");
                $stmt->execute([$description, $amount, $category_id, $subcategory_id, $rate_type, $num_days, true, $original_amount, $learner_multiplier]);
            } else {
                // Save regular cost
                $stmt = $pdo->prepare("INSERT INTO costs (description, amount, category_id, subcategory_id, rate_type, num_days, is_multiplied_by_learners) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([$description, $amount, $category_id, $subcategory_id, $rate_type, $num_days, false]);
            }

            $newId = $pdo->lastInsertId();
            echo json_encode([
                'success' => true,
                'message' => 'Cost added successfully.',
                'id' => $newId,
                'is_multiplied_by_learners' => $is_multiplied_by_learners,
                'learner_count_used' => $learner_multiplier
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Error saving cost: ' . $e->getMessage()
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid input data: ' . implode(', ', $errors)
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method'
    ]);
}
?>
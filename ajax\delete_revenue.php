<?php
// /ajax/delete_revenue.php
require_once __DIR__ . '/../config/db.php';
header('Content-Type: application/json');

// Debug: Log request
error_log("Delete revenue request: " . print_r($_POST, true));

// Get JSON input for POST requests
$input = json_decode(file_get_contents('php://input'), true);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Try to get ID from either JSON input or POST data
    $id = intval($input['id'] ?? $_POST['id'] ?? 0);

    error_log("Delete revenue ID: $id");

    if ($id > 0) {
        try {
            // Begin transaction
            $pdo->beginTransaction();

            // Delete the revenue record
            $stmt = $pdo->prepare("DELETE FROM revenue WHERE id = ?");
            $stmt->execute([$id]);

            if ($stmt->rowCount() > 0) {
                // Recalculate learner-multiplied costs
                include_once __DIR__ . '/recalculate_learner_costs_internal.php';
                $recalcResult = recalculateLearnerCosts($pdo);

                // Commit transaction
                $pdo->commit();

                $message = 'Revenue deleted successfully.';
                if ($recalcResult['updated_costs'] > 0) {
                    $message .= " Automatically updated {$recalcResult['updated_costs']} cost(s) to reflect new learner count.";
                }

                echo json_encode([
                    'success' => true,
                    'message' => $message,
                    'recalculation_result' => $recalcResult
                ]);
            } else {
                $pdo->rollback();
                echo json_encode(['success' => false, 'message' => 'Revenue not found or already deleted.']);
            }
        } catch (Exception $e) {
            // Rollback on error
            if ($pdo->inTransaction()) {
                $pdo->rollback();
            }
            error_log("Error deleting revenue: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Error deleting revenue: ' . $e->getMessage()]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid revenue ID.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
}
?>
<?php
// /admin/analytics.php
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/cost_calculator.php';

// Role-based guard for admin
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
  header('Location: ../auth/login.php');
  exit;
}

// Get analytics data with error handling
try {
    $totalRevenue = $pdo->query("SELECT SUM(revenue_per_student * total_students) as total FROM revenue")->fetch()['total'] ?? 0;
    $totalCosts = calculateTotalCosts($pdo);
} catch (Exception $e) {
    $totalRevenue = 0;
    $totalCosts = 0;
}
$netProfit = $totalRevenue - $totalCosts;
$profitMargin = ($totalRevenue > 0) ? round(($netProfit / $totalRevenue) * 100, 2) : 0;

// Get revenue by month
try {
    $revenueByMonthResult = $pdo->query("
        SELECT
            DATE_FORMAT(month_year, '%Y-%m') as month,
            SUM(revenue_per_student * total_students) as total
        FROM revenue
        GROUP BY month
        ORDER BY month
    ")->fetchAll();
} catch (Exception $e) {
    $revenueByMonthResult = [];
}

$revenueByMonth = [];
foreach ($revenueByMonthResult as $row) {
    $revenueByMonth[$row['month']] = $row['total'];
}

// Get costs by month using new calculation logic
try {
    // Get all unique months from costs
    $monthsResult = $pdo->query("
        SELECT DISTINCT DATE_FORMAT(created_at, '%Y-%m') as month
        FROM costs
        ORDER BY month
    ")->fetchAll();

    $costsByMonth = [];
    foreach ($monthsResult as $row) {
        $month = $row['month'];
        $costsByMonth[$month] = calculateTotalCosts($pdo, $month);
    }
} catch (Exception $e) {
    $costsByMonth = [];
}

// Combine months
$allMonths = array_unique(array_merge(array_keys($revenueByMonth), array_keys($costsByMonth)));
sort($allMonths);

// Format months for display
$formattedMonths = [];
$revenueData = [];
$costsData = [];
$profitData = [];

// Ensure we have at least some data for the charts
if (empty($allMonths)) {
    // If no data, create a placeholder
    $formattedMonths = [date('M Y')];
    $revenueData = [0];
    $costsData = [0];
    $profitData = [0];
} else {
    foreach ($allMonths as $month) {
        $date = new DateTime($month . '-01');
        $formattedMonths[] = $date->format('M Y');

        $rev = $revenueByMonth[$month] ?? 0;
        $cost = $costsByMonth[$month] ?? 0;

        $revenueData[] = floatval($rev);
        $costsData[] = floatval($cost);
        $profitData[] = floatval($rev - $cost);
    }
}

// Get cost breakdown by category
try {
    $costsByCategory = getCostBreakdownByCategory($pdo);
} catch (Exception $e) {
    $costsByCategory = [];
}

// Ensure we have data for cost breakdown
if (empty($costsByCategory)) {
    $costsByCategory = [['category' => 'No Data', 'total' => 0]];
}

// Get revenue by student type
try {
    $revenueByType = $pdo->query("
        SELECT
            student_type,
            SUM(revenue_per_student * total_students) as total
        FROM revenue
        GROUP BY student_type
    ")->fetchAll();
} catch (Exception $e) {
    $revenueByType = [];
}

// Ensure we have data for revenue by type
if (empty($revenueByType)) {
    $revenueByType = [['student_type' => 'No Data', 'total' => 0]];
}
?>

  <main class="container">
    <h1 class="section-title"><i class="fas fa-chart-bar"></i> Analytics Dashboard</h1>

        <!-- Summary Cards -->
        <div class="dashboard-grid">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Total Revenue</h3>
                    <div class="card-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                </div>
                <div class="card-value">R<?= number_format($totalRevenue, 2) ?></div>
                <p class="card-subtitle">All time revenue</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Total Costs</h3>
                    <div class="card-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
                <div class="card-value">R<?= number_format($totalCosts, 2) ?></div>
                <p class="card-subtitle">All time costs</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Net Profit</h3>
                    <div class="card-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="card-value">R<?= number_format($netProfit, 2) ?></div>
                <p class="card-subtitle">Revenue - Costs</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Profit Margin</h3>
                    <div class="card-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                </div>
                <div class="card-value"><?= $profitMargin ?>%</div>
                <p class="card-subtitle">Profit as % of Revenue</p>
            </div>
        </div>

    <!-- Charts -->
    <h2 class="section-subtitle"><i class="fas fa-chart-line"></i> Revenue vs Costs Over Time</h2>
    <div class="card">
      <div class="chart-container" style="height: 400px;">
        <canvas id="revenueChart"></canvas>
      </div>
    </div>

    <div class="chart-grid">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title"><i class="fas fa-chart-pie"></i> Cost Breakdown by Category</h3>
        </div>
        <div class="chart-container">
          <canvas id="costChart"></canvas>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <h3 class="card-title"><i class="fas fa-users"></i> Revenue by Student Type</h3>
        </div>
        <div class="chart-container">
          <canvas id="studentTypeChart"></canvas>
        </div>
      </div>
    </div>

    <h2 class="section-subtitle"><i class="fas fa-chart-bar"></i> Profitability Analysis</h2>
    <div class="card">
      <div class="chart-container" style="height: 400px;">
        <canvas id="profitabilityChart"></canvas>
      </div>
    </div>
  </main>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // Revenue vs Costs chart
      const revenueChartElement = document.getElementById('revenueChart');
      if (revenueChartElement) {
        new Chart(revenueChartElement, {
        type: 'line',
        data: {
          labels: <?= json_encode($formattedMonths) ?>,
          datasets: [
            {
              label: 'Revenue',
              data: <?= json_encode($revenueData) ?>,
              borderColor: '#4361ee',
              backgroundColor: 'rgba(67, 97, 238, 0.1)',
              fill: true
            },
            {
              label: 'Costs',
              data: <?= json_encode($costsData) ?>,
              borderColor: '#f72585',
              backgroundColor: 'rgba(247, 37, 133, 0.1)',
              fill: true
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      });
      }

      // Cost Breakdown chart
      const costChartElement = document.getElementById('costChart');
      if (costChartElement) {
        new Chart(costChartElement, {
        type: 'pie',
        data: {
          labels: <?= json_encode(array_column($costsByCategory, 'category')) ?>,
          datasets: [{
            data: <?= json_encode(array_column($costsByCategory, 'total')) ?>,
            backgroundColor: [
              '#4361ee', '#3a0ca3', '#7209b7', '#f72585', '#4cc9f0',
              '#560bad', '#b5179e', '#480ca8', '#3f37c9', '#4895ef'
            ]
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      });
      }

      // Student Type chart
      const studentTypeChartElement = document.getElementById('studentTypeChart');
      if (studentTypeChartElement) {
        new Chart(studentTypeChartElement, {
        type: 'pie',
        data: {
          labels: <?= json_encode(array_column($revenueByType, 'student_type')) ?>,
          datasets: [{
            data: <?= json_encode(array_column($revenueByType, 'total')) ?>,
            backgroundColor: ['#4361ee', '#f72585']
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      });
      }

      // Profitability chart
      const profitabilityChartElement = document.getElementById('profitabilityChart');
      if (profitabilityChartElement) {
        new Chart(profitabilityChartElement, {
        type: 'bar',
        data: {
          labels: <?= json_encode($formattedMonths) ?>,
          datasets: [{
            label: 'Profit',
            data: <?= json_encode($profitData) ?>,
            backgroundColor: <?= json_encode(array_map(function($profit) {
              return $profit >= 0 ? 'rgba(67, 97, 238, 0.7)' : 'rgba(247, 37, 133, 0.7)';
            }, $profitData)) ?>
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      });
      }
    });
  </script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

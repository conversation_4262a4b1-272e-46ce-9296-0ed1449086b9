<?php
// /admin/analytics.php
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/cost_calculator.php';

// Role-based guard for admin
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
  header('Location: ../auth/login.php');
  exit;
}

// Get analytics data with error handling
try {
    $totalRevenue = $pdo->query("SELECT SUM(revenue_per_student * total_students) as total FROM revenue")->fetch()['total'] ?? 0;
    $totalCosts = calculateTotalCosts($pdo);
} catch (Exception $e) {
    $totalRevenue = 0;
    $totalCosts = 0;
}
$netProfit = $totalRevenue - $totalCosts;
$profitMargin = ($totalRevenue > 0) ? round(($netProfit / $totalRevenue) * 100, 2) : 0;

// Get revenue by month
try {
    $revenueByMonthResult = $pdo->query("
        SELECT
            DATE_FORMAT(month_year, '%Y-%m') as month,
            SUM(revenue_per_student * total_students) as total
        FROM revenue
        GROUP BY month
        ORDER BY month
    ")->fetchAll();
} catch (Exception $e) {
    $revenueByMonthResult = [];
}

$revenueByMonth = [];
foreach ($revenueByMonthResult as $row) {
    $revenueByMonth[$row['month']] = $row['total'];
}

// Get costs by month using new calculation logic
try {
    // Get all unique months from costs
    $monthsResult = $pdo->query("
        SELECT DISTINCT DATE_FORMAT(created_at, '%Y-%m') as month
        FROM costs
        ORDER BY month
    ")->fetchAll();

    $costsByMonth = [];
    foreach ($monthsResult as $row) {
        $month = $row['month'];
        $costsByMonth[$month] = calculateTotalCosts($pdo, $month);
    }
} catch (Exception $e) {
    $costsByMonth = [];
}

// Combine months
$allMonths = array_unique(array_merge(array_keys($revenueByMonth), array_keys($costsByMonth)));
sort($allMonths);

// Format months for display
$formattedMonths = [];
$revenueData = [];
$costsData = [];
$profitData = [];

// Ensure we have at least some data for the charts
if (empty($allMonths)) {
    // If no data, create a placeholder
    $formattedMonths = [date('M Y')];
    $revenueData = [0];
    $costsData = [0];
    $profitData = [0];
} else {
    foreach ($allMonths as $month) {
        $date = new DateTime($month . '-01');
        $formattedMonths[] = $date->format('M Y');

        $rev = $revenueByMonth[$month] ?? 0;
        $cost = $costsByMonth[$month] ?? 0;

        $revenueData[] = floatval($rev);
        $costsData[] = floatval($cost);
        $profitData[] = floatval($rev - $cost);
    }
}

// Get cost breakdown by category
try {
    $costsByCategory = getCostBreakdownByCategory($pdo);
} catch (Exception $e) {
    $costsByCategory = [];
}

// Ensure we have data for cost breakdown
if (empty($costsByCategory)) {
    $costsByCategory = [['category' => 'No Data', 'total' => 0]];
}

// Create aggregated category data for histogram (combines linked costs with parent categories)
function getAggregatedCategoryData($pdo) {
    try {
        $aggregatedData = [];

        // Get all categories with their direct costs (only include categories that are included in cost calculations)
        $directCostsQuery = $pdo->query("
            SELECT
                c.id,
                c.name as category,
                c.include_in_cost,
                COALESCE(SUM(co.amount * co.num_days), 0) as direct_total
            FROM categories c
            LEFT JOIN costs co ON c.id = co.category_id
            WHERE c.include_in_cost = 1
            GROUP BY c.id, c.name, c.include_in_cost
            ORDER BY c.name
        ");
        $directCosts = $directCostsQuery->fetchAll();

        // Initialize aggregated data with direct costs
        foreach ($directCosts as $category) {
            $aggregatedData[$category['category']] = [
                'id' => $category['id'],
                'direct_total' => floatval($category['direct_total']),
                'linked_total' => 0,
                'grand_total' => floatval($category['direct_total'])
            ];
        }

        // Get category links and add linked costs to multiplier categories
        // Only include links where the multiplier category is included in cost calculations
        $linksQuery = $pdo->query("
            SELECT
                cl.multiplier_category_id,
                cl.target_category_id,
                cl.multiplier,
                mc.name as multiplier_category_name,
                mc.include_in_cost as multiplier_include_in_cost,
                tc.name as target_category_name,
                tc.include_in_cost as target_include_in_cost
            FROM category_links cl
            LEFT JOIN categories mc ON cl.multiplier_category_id = mc.id
            LEFT JOIN categories tc ON cl.target_category_id = tc.id
            WHERE mc.include_in_cost = 1
        ");
        $links = $linksQuery->fetchAll();

        foreach ($links as $link) {
            // Check if the multiplier category has any costs (only apply link if it does)
            $hasMultiplierCosts = false;
            if (isset($aggregatedData[$link['multiplier_category_name']])) {
                $hasMultiplierCosts = $aggregatedData[$link['multiplier_category_name']]['direct_total'] > 0;
            }

            if ($hasMultiplierCosts) {
                // Get target category's total costs directly from database (including conditional categories)
                $targetCostsQuery = $pdo->prepare("
                    SELECT COALESCE(SUM(co.amount * co.num_days), 0) as total
                    FROM costs co
                    WHERE co.category_id = ?
                ");
                $targetCostsQuery->execute([$link['target_category_id']]);
                $targetTotal = floatval($targetCostsQuery->fetch()['total'] ?? 0);

                if ($targetTotal > 0) {
                    $linkedAmount = $targetTotal * floatval($link['multiplier']);

                    // Add linked cost to multiplier category (which is guaranteed to be included in costs)
                    $aggregatedData[$link['multiplier_category_name']]['linked_total'] += $linkedAmount;
                    $aggregatedData[$link['multiplier_category_name']]['grand_total'] += $linkedAmount;
                }
            }
        }

        // Convert to array format for charts, excluding categories with zero totals
        $result = [];
        foreach ($aggregatedData as $categoryName => $data) {
            if ($data['grand_total'] > 0) {
                $result[] = [
                    'category' => $categoryName,
                    'total' => $data['grand_total'],
                    'direct_total' => $data['direct_total'],
                    'linked_total' => $data['linked_total']
                ];
            }
        }

        // Sort by total descending
        usort($result, function($a, $b) {
            return $b['total'] <=> $a['total'];
        });

        return $result;
    } catch (Exception $e) {
        error_log("Error getting aggregated category data: " . $e->getMessage());
        return [];
    }
}

// Get aggregated category data for histogram
try {
    $aggregatedCategoryData = getAggregatedCategoryData($pdo);
} catch (Exception $e) {
    $aggregatedCategoryData = [];
}

// Ensure we have data for histogram
if (empty($aggregatedCategoryData)) {
    $aggregatedCategoryData = [['category' => 'No Data', 'total' => 0, 'direct_total' => 0, 'linked_total' => 0]];
}

// Get revenue by student type
try {
    $revenueByType = $pdo->query("
        SELECT
            student_type,
            SUM(revenue_per_student * total_students) as total
        FROM revenue
        GROUP BY student_type
    ")->fetchAll();
} catch (Exception $e) {
    $revenueByType = [];
}

// Ensure we have data for revenue by type
if (empty($revenueByType)) {
    $revenueByType = [['student_type' => 'No Data', 'total' => 0]];
}
?>

  <main class="container">
    <h1 class="section-title"><i class="fas fa-chart-bar"></i> Analytics Dashboard</h1>

        <!-- Summary Cards -->
        <div class="dashboard-grid">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Total Revenue</h3>
                    <div class="card-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                </div>
                <div class="card-value">R<?= number_format($totalRevenue, 2) ?></div>
                <p class="card-subtitle">All time revenue</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Total Costs</h3>
                    <div class="card-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
                <div class="card-value">R<?= number_format($totalCosts, 2) ?></div>
                <p class="card-subtitle">All time costs</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Net Profit</h3>
                    <div class="card-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="card-value">R<?= number_format($netProfit, 2) ?></div>
                <p class="card-subtitle">Revenue - Costs</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Profit Margin</h3>
                    <div class="card-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                </div>
                <div class="card-value"><?= $profitMargin ?>%</div>
                <p class="card-subtitle">Profit as % of Revenue</p>
            </div>
        </div>

    <!-- Charts -->
    <h2 class="section-subtitle"><i class="fas fa-chart-line"></i> Revenue vs Costs Over Time</h2>
    <div class="card">
      <div class="chart-container" style="height: 400px;">
        <canvas id="revenueChart"></canvas>
      </div>
    </div>

    <div class="chart-grid">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title"><i class="fas fa-chart-pie"></i> Cost Breakdown by Category</h3>
        </div>
        <div class="chart-container">
          <canvas id="costChart"></canvas>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <h3 class="card-title"><i class="fas fa-users"></i> Revenue by Student Type</h3>
        </div>
        <div class="chart-container">
          <canvas id="studentTypeChart"></canvas>
        </div>
      </div>
    </div>

    <h2 class="section-subtitle"><i class="fas fa-chart-bar"></i> Category Cost Histogram</h2>
    <div class="card">
      <div class="card-header">
        <h3 class="card-title"><i class="fas fa-chart-bar"></i> All Categories Cost Distribution</h3>
        <p class="card-subtitle">Compare costs across all categories including linked costs</p>
      </div>
      <div class="chart-container" style="height: 400px;">
        <canvas id="categoryHistogram"></canvas>
      </div>
    </div>

    <h2 class="section-subtitle"><i class="fas fa-chart-bar"></i> Profitability Analysis</h2>
    <div class="card">
      <div class="chart-container" style="height: 400px;">
        <canvas id="profitabilityChart"></canvas>
      </div>
    </div>
  </main>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // Revenue vs Costs chart
      const revenueChartElement = document.getElementById('revenueChart');
      if (revenueChartElement) {
        new Chart(revenueChartElement, {
        type: 'line',
        data: {
          labels: <?= json_encode($formattedMonths) ?>,
          datasets: [
            {
              label: 'Revenue',
              data: <?= json_encode($revenueData) ?>,
              borderColor: '#4361ee',
              backgroundColor: 'rgba(67, 97, 238, 0.1)',
              fill: true
            },
            {
              label: 'Costs',
              data: <?= json_encode($costsData) ?>,
              borderColor: '#f72585',
              backgroundColor: 'rgba(247, 37, 133, 0.1)',
              fill: true
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      });
      }

      // Cost Breakdown chart
      const costChartElement = document.getElementById('costChart');
      if (costChartElement) {
        new Chart(costChartElement, {
        type: 'pie',
        data: {
          labels: <?= json_encode(array_column($costsByCategory, 'category')) ?>,
          datasets: [{
            data: <?= json_encode(array_column($costsByCategory, 'total')) ?>,
            backgroundColor: [
              '#4361ee', '#3a0ca3', '#7209b7', '#f72585', '#4cc9f0',
              '#560bad', '#b5179e', '#480ca8', '#3f37c9', '#4895ef'
            ]
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      });
      }

      // Student Type chart
      const studentTypeChartElement = document.getElementById('studentTypeChart');
      if (studentTypeChartElement) {
        new Chart(studentTypeChartElement, {
        type: 'pie',
        data: {
          labels: <?= json_encode(array_column($revenueByType, 'student_type')) ?>,
          datasets: [{
            data: <?= json_encode(array_column($revenueByType, 'total')) ?>,
            backgroundColor: ['#4361ee', '#f72585']
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      });
      }

      // Category Histogram chart
      const categoryHistogramElement = document.getElementById('categoryHistogram');
      if (categoryHistogramElement) {
        const aggregatedData = <?= json_encode($aggregatedCategoryData) ?>;

        new Chart(categoryHistogramElement, {
          type: 'bar',
          data: {
            labels: aggregatedData.map(item => item.category),
            datasets: [{
              label: 'Total Cost (R)',
              data: aggregatedData.map(item => item.total),
              backgroundColor: [
                '#4361ee', '#3a0ca3', '#7209b7', '#f72585', '#4cc9f0',
                '#560bad', '#b5179e', '#480ca8', '#3f37c9', '#4895ef',
                '#06ffa5', '#ffbe0b', '#fb5607', '#8338ec', '#3a86ff'
              ],
              borderColor: [
                '#4361ee', '#3a0ca3', '#7209b7', '#f72585', '#4cc9f0',
                '#560bad', '#b5179e', '#480ca8', '#3f37c9', '#4895ef',
                '#06ffa5', '#ffbe0b', '#fb5607', '#8338ec', '#3a86ff'
              ],
              borderWidth: 2,
              borderRadius: 4,
              borderSkipped: false
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                callbacks: {
                  title: function(context) {
                    return context[0].label;
                  },
                  label: function(context) {
                    const dataIndex = context.dataIndex;
                    const item = aggregatedData[dataIndex];

                    const total = 'Total: R' + item.total.toLocaleString('en-ZA', {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2
                    });

                    return total;
                  },
                  afterLabel: function(context) {
                    const dataIndex = context.dataIndex;
                    const item = aggregatedData[dataIndex];

                    const lines = [];

                    if (item.direct_total > 0) {
                      lines.push('Direct Costs: R' + item.direct_total.toLocaleString('en-ZA', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                      }));
                    }

                    if (item.linked_total > 0) {
                      lines.push('Linked Costs: R' + item.linked_total.toLocaleString('en-ZA', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                      }));
                    }

                    return lines;
                  }
                }
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  callback: function(value) {
                    return 'R' + value.toLocaleString('en-ZA');
                  }
                },
                title: {
                  display: true,
                  text: 'Cost Amount (R)'
                }
              },
              x: {
                title: {
                  display: true,
                  text: 'Categories (Including Linked Costs)'
                },
                ticks: {
                  maxRotation: 45,
                  minRotation: 0
                }
              }
            },
            animation: {
              duration: 1000,
              easing: 'easeInOutQuart'
            }
          }
        });
      }

      // Profitability chart
      const profitabilityChartElement = document.getElementById('profitabilityChart');
      if (profitabilityChartElement) {
        new Chart(profitabilityChartElement, {
        type: 'bar',
        data: {
          labels: <?= json_encode($formattedMonths) ?>,
          datasets: [{
            label: 'Profit',
            data: <?= json_encode($profitData) ?>,
            backgroundColor: <?= json_encode(array_map(function($profit) {
              return $profit >= 0 ? 'rgba(67, 97, 238, 0.7)' : 'rgba(247, 37, 133, 0.7)';
            }, $profitData)) ?>
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      });
      }
    });
  </script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

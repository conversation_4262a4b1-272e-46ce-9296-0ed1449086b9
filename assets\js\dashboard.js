// /assets/js/dashboard.js

document.addEventListener("DOMContentLoaded", () => {
  console.log("Dashboard.js loaded");

  // === NAVIGATION TAB SWITCHING ===
  // Only target internal tab links
  const tabs    = document.querySelectorAll("main section.tab");
  const navLinks = document.querySelectorAll('nav a[href^="#"]');

  // Function to initialize analytics charts
  function initAnalytics() {
    console.log("Initializing analytics...");

    const revenueChart = document.getElementById('revenueChart');
    const costChart = document.getElementById('costChart');
    const studentTypeChart = document.getElementById('studentTypeChart');
    const profitabilityChart = document.getElementById('profitabilityChart');

    if (revenueChart && costChart && studentTypeChart && profitabilityChart) {
      console.log("Found chart elements, fetching data...");

      // First test if we can access the AJAX endpoint
      const testUrl = '../ajax/test.php';
      console.log("Testing AJAX access with:", testUrl);

      fetch(testUrl)
        .then(response => {
          console.log("Test response received:", response.status);
          return response.json();
        })
        .then(data => {
          console.log("Test endpoint working:", data);

          // Now fetch the actual analytics data
          const analyticsUrl = '../ajax/get_analytics_data.php';
          console.log("Fetching analytics data from:", analyticsUrl);
          return fetch(analyticsUrl);
        })
        .then(response => {
          console.log("Response received:", response.status);
          return response.json();
        })
        .then(data => {
          console.log('Analytics data:', data);

          if (data.success) {
            // Update summary cards
            document.getElementById('total-revenue').textContent = `R${data.summary.totalRevenue.toLocaleString()}`;
            document.getElementById('total-costs').textContent = `R${data.summary.totalCosts.toLocaleString()}`;
            document.getElementById('total-profit').textContent = `R${data.summary.netProfit.toLocaleString()}`;
            document.getElementById('profit-margin').textContent = `${data.summary.profitMargin}%`;

            // Create Revenue vs Costs chart
            new Chart(revenueChart, {
              type: 'line',
              data: {
                labels: data.revenueVsCosts.labels,
                datasets: [
                  {
                    label: 'Revenue',
                    data: data.revenueVsCosts.revenue,
                    borderColor: '#4361ee',
                    backgroundColor: 'rgba(67, 97, 238, 0.1)',
                    fill: true
                  },
                  {
                    label: 'Costs',
                    data: data.revenueVsCosts.costs,
                    borderColor: '#f72585',
                    backgroundColor: 'rgba(247, 37, 133, 0.1)',
                    fill: true
                  }
                ]
              },
              options: {
                responsive: true,
                maintainAspectRatio: false
              }
            });

            // Create Cost Breakdown chart
            new Chart(costChart, {
              type: 'pie',
              data: {
                labels: data.costBreakdown.labels,
                datasets: [{
                  data: data.costBreakdown.values,
                  backgroundColor: [
                    '#4361ee', '#3a0ca3', '#7209b7', '#f72585', '#4cc9f0',
                    '#560bad', '#b5179e', '#480ca8', '#3f37c9', '#4895ef'
                  ]
                }]
              },
              options: {
                responsive: true,
                maintainAspectRatio: false
              }
            });

            // Create Student Type chart
            new Chart(studentTypeChart, {
              type: 'pie',
              data: {
                labels: data.studentTypes.labels,
                datasets: [{
                  data: data.studentTypes.values,
                  backgroundColor: ['#4361ee', '#f72585']
                }]
              },
              options: {
                responsive: true,
                maintainAspectRatio: false
              }
            });

            // Create Profitability chart
            new Chart(profitabilityChart, {
              type: 'bar',
              data: {
                labels: data.profitability.labels,
                datasets: [{
                  label: 'Profit',
                  data: data.profitability.values,
                  backgroundColor: data.profitability.values.map(value =>
                    value >= 0 ? 'rgba(67, 97, 238, 0.7)' : 'rgba(247, 37, 133, 0.7)'
                  )
                }]
              },
              options: {
                responsive: true,
                maintainAspectRatio: false
              }
            });

            // Create subcategory breakdown
            const subcategoryBreakdown = document.getElementById('subcategory-breakdown');
            if (subcategoryBreakdown) {
              let html = '';
              data.subcategoryBreakdown.forEach(category => {
                html += `
                  <div class="breakdown-category">
                    <h4>${category.name}</h4>
                    <div class="breakdown-subcategories">
                `;

                category.subcategories.forEach(sub => {
                  html += `
                    <div class="breakdown-item">
                      <div class="breakdown-name">${sub.name}</div>
                      <div class="breakdown-value">R${sub.value.toLocaleString()}</div>
                      <div class="breakdown-bar">
                        <div class="breakdown-bar-fill" style="width: ${sub.percentage}%"></div>
                      </div>
                      <div class="breakdown-percentage">${sub.percentage}%</div>
                    </div>
                  `;
                });

                html += `
                    </div>
                  </div>
                `;
              });

              subcategoryBreakdown.innerHTML = html;
            }
          } else {
            console.error('Failed to load analytics data:', data.message);
          }
        })
        .catch(error => {
          console.error('Error fetching analytics data:', error);
        });
    } else {
      console.error("Could not find chart elements");
    }
  }

  function showTab(id) {
    tabs.forEach(tab => {
      tab.style.display = tab.id === id ? "block" : "none";
    });

    // Initialize analytics if analytics tab is shown
    if (id === 'analytics') {
      initAnalytics();
    }
  }

  navLinks.forEach(link => {
    link.addEventListener("click", e => {
      e.preventDefault();
      const id = link.getAttribute("href").slice(1);
      showTab(id);
      // mark active
      navLinks.forEach(l => l.classList.remove("active"));
      link.classList.add("active");
    });
  });

  // Check if a specific tab is requested in the URL hash
  const hash = window.location.hash.substring(1);
  if (hash && document.getElementById(hash)) {
    showTab(hash);
    document.querySelector(`nav a[href="#${hash}"]`)?.classList.add("active");
  } else {
    // Default to the Costs tab
    showTab("costs");
    document.querySelector('nav a[href="#costs"]')?.classList.add("active");
  }

  // === COST ENTRY ===
  const costForm         = document.getElementById("cost-form");
  const costMsg          = document.getElementById("cost-message");
  const categorySelect   = document.getElementById("category-select");
  const subcategorySelect = document.getElementById("subcategory-select");

  if (categorySelect) {
    // Load categories
    fetch("../ajax/get_categories.php")
      .then(res => res.json())
      .then(data => {
        categorySelect.innerHTML = '<option value="">Select Category</option>';
        console.log("Categories data:", data); // Debug
        if (Array.isArray(data)) {
          data.forEach(cat => {
            const opt = document.createElement("option");
            opt.value       = cat.id;
            opt.textContent = cat.name;
            categorySelect.appendChild(opt);
          });
        } else {
          console.error("Categories data is not an array:", data);
        }
      })
      .catch(err => {
        console.error("Error loading categories:", err);
      });

    // When category changes, load subcategories
    categorySelect.addEventListener("change", () => {
      const catId = categorySelect.value;
      if (!catId) {
        subcategorySelect.innerHTML = '<option value="">Select Category First</option>';
        return;
      }

      subcategorySelect.innerHTML = '<option>Loading…</option>';
      fetch(`../ajax/get_subcategories.php?category_id=${catId}`)
        .then(res => res.json())
        .then(data => {
          console.log("Subcategories data:", data); // Debug
          subcategorySelect.innerHTML = '<option value="">Select Subcategory</option>';

          if (data.success && Array.isArray(data.subcategories)) {
            data.subcategories.forEach(sub => {
              const opt = document.createElement("option");
              opt.value       = sub.id;
              opt.textContent = sub.name;
              subcategorySelect.appendChild(opt);
            });

            if (data.subcategories.length === 0) {
              subcategorySelect.innerHTML = '<option value="">No subcategories available</option>';
            }
          } else {
            console.error("Invalid subcategories data:", data);
            subcategorySelect.innerHTML = '<option value="">Error loading subcategories</option>';
          }
        })
        .catch(err => {
          console.error("Error loading subcategories:", err);
          subcategorySelect.innerHTML = '<option value="">Error loading subcategories</option>';
        });
    });
  }

  // Only handle cost form if we're NOT on the costs page AND no other handler is attached
  if (costForm && !window.location.pathname.includes('/costs.php') && !window.costFormHandled) {
    costForm.addEventListener("submit", async e => {
      e.preventDefault();
      try {
        const fd = new FormData(costForm);
        const res = await fetch("../ajax/save_cost.php", { method: "POST", body: fd });
        const data = await res.json();

        // Show toast notification instead of text message
        if (data.success) {
          // Show success toast
          showToast(data.message, "success", 3000);

          // Reset the form
          costForm.reset();

          // Refresh the page after a short delay to show the updated table
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } else {
          // Show error toast
          showToast(data.message || "An error occurred", "error", 5000);
        }
      } catch (error) {
        console.error("Error saving cost:", error);
        showToast("Failed to save cost. Please try again.", "error", 5000);
      }
    });
  }

  // === REVENUE ENTRY ===
  const revenueForm = document.getElementById("revenue-form");
  const revenueMsg  = document.getElementById("revenue-message");
  if (revenueForm) {
    revenueForm.addEventListener("submit", async e => {
      e.preventDefault();
      try {
        const fd = new FormData(revenueForm);
        const res = await fetch("../ajax/save_revenue.php", { method: "POST", body: fd });
        const data = await res.json();

        // Show toast notification instead of text message
        if (data.success) {
          // Show success toast
          showToast(data.message, "success", 5000);

          // Show additional info if costs were recalculated
          if (data.recalculation_result && data.recalculation_result.updated_costs > 0) {
            setTimeout(() => {
              showToast(`🔄 ${data.recalculation_result.updated_costs} cost(s) automatically recalculated for ${data.recalculation_result.current_learner_count} learners`, "info", 4000);
            }, 1000);
          }

          // Reset the form
          revenueForm.reset();

          // Notify other tabs/pages that revenue data has changed
          localStorage.setItem('revenueDataChanged', Date.now().toString());

          // Refresh the page after a short delay to show the updated table
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } else {
          // Show error toast
          showToast(data.message || "An error occurred", "error", 5000);
        }
      } catch (error) {
        console.error("Error saving revenue:", error);
        showToast("Failed to save revenue. Please try again.", "error", 5000);
      }
    });
  }



  // === CHARTS & ANALYTICS ===
  // Analytics initialization is now handled by the showTab function

});

<?php
// /ajax/save_category_link.php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../includes/auth.php';

// Ensure user is authenticated as admin
authenticate_admin();

header('Content-Type: application/json');

$debug = [];

try {
  // Get form data
  $multiplier_category_id = intval($_POST['multiplier_category_id'] ?? 0);
  $target_category_id = intval($_POST['target_category_id'] ?? 0);
  $multiplier = floatval($_POST['multiplier'] ?? 1.0);

  $debug['multiplier_category_id'] = $multiplier_category_id;
  $debug['target_category_id'] = $target_category_id;
  $debug['multiplier'] = $multiplier;

  // Validate input
  if ($multiplier_category_id <= 0) {
    throw new Exception('Valid multiplier category is required.');
  }

  if ($target_category_id <= 0) {
    throw new Exception('Valid target category is required.');
  }

  if ($multiplier <= 0) {
    throw new Exception('Multiplier must be greater than zero.');
  }

  if ($multiplier_category_id === $target_category_id) {
    throw new Exception('A category cannot be linked to itself.');
  }

  // Check if both categories exist
  $check = $pdo->prepare("SELECT id, name, include_in_cost FROM categories WHERE id IN (?, ?)");
  $check->execute([$multiplier_category_id, $target_category_id]);
  $categories = $check->fetchAll(PDO::FETCH_ASSOC);
  
  if (count($categories) !== 2) {
    throw new Exception('One or both categories do not exist.');
  }

  // Find target category and validate it's excluded from cost calculations
  $target_category = null;
  $multiplier_category = null;
  foreach ($categories as $cat) {
    if ($cat['id'] == $target_category_id) {
      $target_category = $cat;
    } else {
      $multiplier_category = $cat;
    }
  }

  if ($target_category['include_in_cost']) {
    throw new Exception('Target category must be excluded from cost calculations (include_in_cost = false) to be linked.');
  }

  // Check if link already exists
  $check = $pdo->prepare("SELECT id FROM category_links WHERE multiplier_category_id = ? AND target_category_id = ?");
  $check->execute([$multiplier_category_id, $target_category_id]);
  if ($check->fetch()) {
    throw new Exception('This category link already exists.');
  }

  // Insert the new category link
  $stmt = $pdo->prepare("INSERT INTO category_links (multiplier_category_id, target_category_id, multiplier) VALUES (?, ?, ?)");
  $result = $stmt->execute([$multiplier_category_id, $target_category_id, $multiplier]);

  if (!$result) {
    throw new Exception('Failed to add category link to database.');
  }

  $linkId = $pdo->lastInsertId();
  $debug['link_id'] = $linkId;

  // Return success response
  echo json_encode([
    'success' => true,
    'message' => 'Category link added successfully.',
    'link' => [
      'id' => $linkId,
      'multiplier_category_id' => $multiplier_category_id,
      'multiplier_category_name' => $multiplier_category['name'],
      'target_category_id' => $target_category_id,
      'target_category_name' => $target_category['name'],
      'multiplier' => $multiplier
    ],
    'debug' => $debug
  ]);

} catch (Exception $e) {
  // Return error response
  echo json_encode([
    'success' => false,
    'message' => $e->getMessage(),
    'debug' => $debug
  ]);
}
?>

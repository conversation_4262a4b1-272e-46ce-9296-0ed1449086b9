<?php
// /test_buttons.php
// Test page for button functionality
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Test - Cost Calculator</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Button Functionality Test</h1>
        
        <div class="test-section">
            <h2>Button Styles Test</h2>
            <div class="button-grid">
                <button class="btn btn-primary">
                    <i class="fas fa-plus"></i> Primary Button
                </button>
                <button class="btn btn-secondary">
                    <i class="fas fa-edit"></i> Secondary Button
                </button>
                <button class="btn btn-success">
                    <i class="fas fa-check"></i> Success Button
                </button>
                <button class="btn btn-warning">
                    <i class="fas fa-exclamation"></i> Warning Button
                </button>
                <button class="btn btn-sm">
                    <i class="fas fa-star"></i> Small Button
                </button>
                <button class="btn btn-lg">
                    <i class="fas fa-rocket"></i> Large Button
                </button>
            </div>
        </div>

        <div class="test-section">
            <h2>Form Submission Test</h2>
            <form id="test-form" action="javascript:;">
                <div class="form-group">
                    <label for="test-input">Test Input</label>
                    <input type="text" id="test-input" name="test_input" placeholder="Enter some text" required>
                </div>
                <div class="form-group">
                    <label for="test-textarea">Test Textarea</label>
                    <textarea id="test-textarea" name="test_textarea" placeholder="Enter description"></textarea>
                </div>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="test-checkbox" name="test_checkbox">
                        <span class="checkmark"></span>
                        Test Checkbox
                    </label>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i> Submit Test Form
                </button>
            </form>
            <div id="form-result" style="margin-top: 20px;"></div>
        </div>

        <div class="test-section">
            <h2>AJAX Test</h2>
            <button id="ajax-test-btn" class="btn btn-primary">
                <i class="fas fa-cloud"></i> Test AJAX Request
            </button>
            <div id="ajax-result" style="margin-top: 20px;"></div>
        </div>

        <div class="test-section">
            <h2>Navigation Links</h2>
            <div class="button-grid">
                <a href="admin/categories.php" class="btn btn-primary">
                    <i class="fas fa-tags"></i> Categories Page
                </a>
                <a href="admin/category_links.php" class="btn btn-secondary">
                    <i class="fas fa-link"></i> Category Links
                </a>
                <a href="switch_environment.php" class="btn btn-success">
                    <i class="fas fa-exchange-alt"></i> Environment Switcher
                </a>
                <a href="test_environment.php" class="btn btn-warning">
                    <i class="fas fa-cog"></i> Environment Test
                </a>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Test form submission
        const testForm = document.getElementById('test-form');
        const formResult = document.getElementById('form-result');
        
        testForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(testForm);
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            formResult.innerHTML = `
                <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;">
                    <h4>✅ Form Submission Test Successful!</h4>
                    <p><strong>Data received:</strong></p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
            
            // Reset form after 3 seconds
            setTimeout(() => {
                testForm.reset();
                formResult.innerHTML = '';
            }, 3000);
        });
        
        // Test AJAX request
        const ajaxTestBtn = document.getElementById('ajax-test-btn');
        const ajaxResult = document.getElementById('ajax-result');
        
        ajaxTestBtn.addEventListener('click', async function() {
            const originalText = ajaxTestBtn.innerHTML;
            ajaxTestBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
            ajaxTestBtn.disabled = true;
            
            try {
                // Test with a simple endpoint
                const response = await fetch('ajax/get_categories.php');
                const data = await response.json();
                
                if (data.success || Array.isArray(data)) {
                    ajaxResult.innerHTML = `
                        <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;">
                            <h4>✅ AJAX Test Successful!</h4>
                            <p>Successfully connected to AJAX endpoint.</p>
                            <p><strong>Response:</strong> ${Array.isArray(data) ? data.length + ' categories found' : 'Success'}</p>
                        </div>
                    `;
                } else {
                    ajaxResult.innerHTML = `
                        <div style="background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px;">
                            <h4>⚠️ AJAX Response Received</h4>
                            <p>Got response but format unexpected.</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                ajaxResult.innerHTML = `
                    <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;">
                        <h4>❌ AJAX Test Failed</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>Check browser console for more details.</p>
                    </div>
                `;
            } finally {
                ajaxTestBtn.innerHTML = originalText;
                ajaxTestBtn.disabled = false;
            }
        });
        
        // Test all buttons for click events
        const allButtons = document.querySelectorAll('button');
        allButtons.forEach(button => {
            if (!button.id || (button.id !== 'ajax-test-btn' && button.type !== 'submit')) {
                button.addEventListener('click', function() {
                    console.log('Button clicked:', button.textContent.trim());
                    
                    // Visual feedback
                    const originalBg = button.style.backgroundColor;
                    button.style.backgroundColor = '#28a745';
                    setTimeout(() => {
                        button.style.backgroundColor = originalBg;
                    }, 200);
                });
            }
        });
    });
    </script>
</body>
</html>
